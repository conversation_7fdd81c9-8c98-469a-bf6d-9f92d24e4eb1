CREATE OR REPLACE FUNCTION public.tms_get_srvc_req_details(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	is_prvdr bool;
	applicable_sbbtsk_types_ int[];
	sp_config_details_ json;
	user_roles_ int[];
	roles_list_who_cant_download_site_attendance_ int[];
	allow_site_attendance_download boolean default true;
	roles_list_who_cant_download_sp_daily_updates_ int[];
    allow_sp_daily_updates_download boolean default true;
    role_list_who_cannot_view_line_item_tab_ int[];
    allow_to_see_line_item_tab bool default true;
   	role_list_who_can_add_sp_payouts_tab_ int[];
   	role_list_who_cannot_view_sp_payouts_tab_ int[];
    role_list_fr_vendor_roles_ int[];
	user_list_fr_sp_vendors_ json;
	user_list_fr_add_access_sp_payouts_ json;
	dissallowed_sbtsk_types json;
   	
   	is_sp_sbtsk_assigned bool;
    user_access_service_routes json;

--  Specific
	srvc_type_id_ integer;	
-- Entry id
	entry_id bigint;

	roles_list_who_cant_download_sp_line_items_ int[];
	user_list_who_can_add_payout_ json;
    allow_sp_line_items_download boolean default true;
   	allow_to_add_sp_payout_tab boolean default true;
   	allow_to_see_sp_payout_tab boolean default true;
	add_sp_payout_user_type text;

begin 
	
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(form_data_,'org_id');
	
	usr_id_ = json_extract_path_text(form_data_,'usr_id');
	ip_address_ = json_extract_path_text(form_data_,'ip_address');
	user_agent_ = json_extract_path_text(form_data_,'user_agent');
	srvc_type_id_ = json_extract_path_text(form_data_,'srvc_type_id');
	entry_id = json_extract_path_text(form_data_,'entry_id');

	-- Get user access types 
	user_access_service_routes = tms_hlpr_get_usr_access_service_routes(usr_id_);
	
	-- Check if the org is prvdr or 
	select case
				when srvc_req.org_id = org_id_ then false
				when srvc_req.srvc_prvdr = org_id_ then true
		   end
	  from public.cl_tx_srvc_req as srvc_req
	 where srvc_req.db_id = entry_id
	  into is_prvdr;
	 
	is_sp_sbtsk_assigned =  tms_hlpr_get_is_sp_sbtsk_assigned(entry_id,srvc_type_id_,org_id_); 
	user_roles_ = tms_hlpr_get_user_role_ids(usr_id_);
	 
--	raise notice 'is_prvdr %',is_prvdr;
	sp_config_details_ = tms_hlpr_get_sp_config_details(org_id_, srvc_type_id_);
	if is_prvdr then			
		applicable_sbbtsk_types_ = array(SELECT json_array_elements_text(json_extract_path(sp_config_details_->'settings_data','applicable_subtask_types_fr_vertical')))::integer[];
		roles_list_who_cant_download_site_attendance_ = array(SELECT json_array_elements_text(json_extract_path(sp_config_details_->'settings_data','who_cannot_download_site_level_attendance')))::integer[];
	    roles_list_who_cant_download_sp_daily_updates_ = array(SELECT json_array_elements_text(json_extract_path(sp_config_details_->'settings_data','who_cannot_download_sp_daily_updates')))::integer[];
		roles_list_who_cant_download_sp_line_items_ = array(SELECT json_array_elements_text(json_extract_path(sp_config_details_->'settings_data','who_cannot_download_sp_line_item')))::integer[];
	
		role_list_who_cannot_view_line_item_tab_ = array(select json_array_elements_text(sp_config_details_->'settings_data'->'who_cannot_view_line_items_tab'))::int[];
		role_list_who_can_add_sp_payouts_tab_ = array(select json_array_elements_text(sp_config_details_->'settings_data'->'which_sp_authority_can_add_sp_payout'))::int[];
   		role_list_who_cannot_view_sp_payouts_tab_ = array(select json_array_elements_text(sp_config_details_->'settings_data'->'sp_who_will_not_be_able_to_see_the_sp_payout_tab'))::int[];
		role_list_fr_vendor_roles_ = array(select json_array_elements_text(sp_config_details_->'settings_data'->'sp_vendor_roles'))::int[];
		add_sp_payout_user_type = (sp_config_details_->'settings_data'->>'select_who_can_add_payout');
		user_list_who_can_add_payout_ = (sp_config_details_->'settings_data'->'which_static_user_can_add_sp_payout')::json;
   	end if;

	if roles_list_who_cant_download_site_attendance_ is not null and
	   cardinality(roles_list_who_cant_download_site_attendance_) > 0 then 
    	allow_site_attendance_download = tms_hlpr_array_has_unmatched_element(user_roles_,roles_list_who_cant_download_site_attendance_);
	end if;

	if roles_list_who_cant_download_sp_daily_updates_ is not null 
		and cardinality(roles_list_who_cant_download_sp_daily_updates_) > 0 then 
    		allow_sp_daily_updates_download = tms_hlpr_array_has_unmatched_element(user_roles_,roles_list_who_cant_download_sp_daily_updates_);
	end if;
--who can see line item tab
    if role_list_who_cannot_view_line_item_tab_ is not null 
		and cardinality(role_list_who_cannot_view_line_item_tab_) > 0 then 
    		allow_to_see_line_item_tab = tms_hlpr_array_has_unmatched_element(user_roles_,role_list_who_cannot_view_line_item_tab_);
	end if;
	
	if roles_list_who_cant_download_sp_line_items_ is not null 
		and cardinality(roles_list_who_cant_download_sp_line_items_) > 0 then 
    		allow_sp_line_items_download = tms_hlpr_array_has_unmatched_element(user_roles_,roles_list_who_cant_download_sp_line_items_);
	end if;

--who can see & add sp payouts tab

	--who can see & add sp payouts tab
	if add_sp_payout_user_type = 'authority' 
		and role_list_who_can_add_sp_payouts_tab_ is not null 
		and cardinality(role_list_who_can_add_sp_payouts_tab_) > 0 then
			allow_to_add_sp_payout_tab = tms_hlpr_get_sp_authorities_fr_sp_payout(entry_id,role_list_who_can_add_sp_payouts_tab_, usr_id_);
	elseif add_sp_payout_user_type = 'static_user' 
		and user_list_who_can_add_payout_ is not null then
		allow_to_add_sp_payout_tab = EXISTS (
	        SELECT 1
	        FROM json_array_elements(user_list_who_can_add_payout_::json) AS elem
	        WHERE (elem->>'value')::uuid = usr_id_
	    );
	end if;
	if role_list_who_cannot_view_sp_payouts_tab_ is not null 
		and cardinality(role_list_who_cannot_view_sp_payouts_tab_) > 0 then 
    		allow_to_see_sp_payout_tab = tms_hlpr_array_has_unmatched_element(user_roles_,role_list_who_cannot_view_sp_payouts_tab_);
	end if;

	if role_list_fr_vendor_roles_ is not null 
		and cardinality(role_list_fr_vendor_roles_) > 0 then 
    		user_list_fr_sp_vendors_ = tms_hlpr_get_sp_vendors_fr_sp_payouts(entry_id, role_list_fr_vendor_roles_);
	end if;

	dissallowed_sbtsk_types = tms_hlpr_get_user_subtask_creation_restrictions(usr_id_, org_id_);

--	raise notice 
	resp_data = array_to_json(array(
			select jsonb_build_object(
	    				'id', srvc_req.db_id ,
	    				'org_id',srvc_req.org_id ,
	    				'org_nick_name',orgs_.nickname ,
	    				'title', srvc_req.display_code ,
	    				'srvc_type_title',srvc_type.title,
--	    				'status', srvc_req.status,
	    				'priority', srvc_req.priority,
	    				'is_deleted',srvc_req.is_deleted,
	    				'srvc_type_id', srvc_req.srvc_type_id,
	    				'status', jsonb_build_object(
							'status_type', srvc_status.status_type,
							'title',srvc_status.title,
							'key', srvc_status.status_key,
							'color', srvc_status.color 
						),
						'form_data', srvc_req.form_data, 
						'sbtsk_types',jsonb_agg( 
							jsonb_build_object(
								'value',sbtsk_type.sbtsk_type_id :: text,
								'label',sbtsk_type.title,
								'icon', sbtsk_type.icon_code
							) 
							order by sbtsk_type.sbtsk_type_id
						),
						'status_transitions', array_to_json(array(
							select jsonb_build_object(
										'id', srvc_txn_log.db_id,
										'key', srvc_txn_log.status_key,
										'details', tms_get_srvc_status_details(srvc_req.srvc_type_id,srvc_txn_log.status_key),
										'time', srvc_txn_log.trnstn_date ,
										'c_by', txn_log_c_by."name",
										'u_by', txn_log_u_by."name",
										'c_meta', srvc_txn_log.c_meta,
										'u_meta', srvc_txn_log.u_meta
								   )
						      from cl_tx_srvc_req_trnstn_log as srvc_txn_log
		                     inner join cl_tx_users txn_log_c_by
		                        on txn_log_c_by.usr_id  = srvc_txn_log.c_by
		                     inner join cl_cf_srvc_statuses srvc_statuses
		                        on srvc_statuses.srvc_id = srvc_req.srvc_type_id
				   			   and srvc_statuses.status_key = srvc_txn_log.status_key
		                      left join cl_tx_users txn_log_u_by
		                        on txn_log_u_by.usr_id  = srvc_txn_log.u_by
		                     where srvc_txn_log.srvc_req_id = srvc_req.db_id 
		                     order by srvc_txn_log.trnstn_date,srvc_txn_log.db_id 
						)),
						'subtaskConfigData' ,array_to_json(array(
					    	 select jsonb_build_object(
					    				'value',sbtsk_type.sbtsk_type_id,
					    				'config_data',sbtsk_type.form_data,
					    				'statuses',jsonb_agg(
					    					distinct 
					    					jsonb_build_object(
					    						'value',sbtsk_statuses.status_key,
					    						'title',sbtsk_statuses.title,
					    						'color', sbtsk_statuses.color 
					    					) 
					    				) 
					    		    ) 
						       from cl_tx_srvc_req as srvc_req 
						      inner join cl_tx_sbtsk as sbtsk
						         on sbtsk.srvc_req_id = srvc_req.db_id 
						      inner join cl_cf_sbtsk_types as sbtsk_type  
						         on sbtsk_type.sbtsk_type_id = sbtsk.sbtsk_type 
						      inner join cl_cf_sbtsk_statuses as sbtsk_statuses
						         on sbtsk_statuses.sbtsk_type_id = sbtsk_type.sbtsk_type_id
						      where srvc_req.db_id = entry_id
							  group by sbtsk_type.sbtsk_type_id 
							  order by sbtsk_type.sbtsk_type_id desc
						)),
						'sp_config_data', array_to_json(array(
							select sp_config_details_
						)),
						'discount_approved_by_name', tms_get_user_details((srvc_req.form_data->>'discount_approved_by')::uuid)->'data'->>'user_name' ,
						'sp_discount_approved_by_name', tms_get_user_details((srvc_req.form_data->>'sp_discount_approved_by')::uuid)->'data'->>'user_name',
						'srvc_req_locked_by_name', tms_get_user_details((srvc_req.form_data->>'srvc_req_locked_by')::uuid)->'data'->>'user_name',
						'sp_srvc_req_locked_by_name', tms_get_user_details((srvc_req.form_data->>'sp_srvc_req_locked_by')::uuid)->'data'->>'user_name',
						'location_grp_ids', get_tms_hlpr_loc_grp_ids_by_pincode(org_id_,srvc_req.form_data->>'cust_pincode'),
						'access_service_routes', user_access_service_routes,
						'srvc_type_history_pricing_config', hlpr_tms_get_srvc_type_history_price_config( (srvc_req.form_data->>'srvc_type_his_db_id')::int),
						'sp_srvc_type_history_pricing_config', hlpr_tms_get_srvc_type_history_price_config( (srvc_req.form_data->>'sp_srvc_type_his_db_id')::int, true),
						'billing_final_amount', tms_hlpr_get_billing_final_amount(org_id_,srvc_req),
						'send_for_billing_by_name', tms_get_user_details((srvc_req.form_data->>'send_for_billing_u_by')::uuid)->'data'->>'user_name',
						'sp_send_for_billing_by_name', tms_get_user_details((srvc_req.form_data->>'sp_send_for_billing_u_by')::uuid)->'data'->>'user_name',
						'is_enable_auto_assign_authorities_refresh_btn', tms_hlpr_is_enable_auto_assign_authorities_refresh_btn(org_id_,srvc_req.db_id, srvc_req.srvc_type_id),
						'revisions_data', tms_hlpr_get_srvc_req_history_type_data(srvc_req.db_id,'LINE_ITEMS_REVISIONS'),
	    		   		'sp_revisions_data', tms_hlpr_get_srvc_req_history_type_data(srvc_req.db_id,'SP_LINE_ITEMS_REVISIONS'),
						'all_ratings',tms_hlpr_get_all_ratings_fr_srvc_req(entry_id,org_id_,form_data_),
						'sp_all_ratings',tms_hlpr_get_all_ratings_fr_srvc_req(entry_id,srvc_req.srvc_prvdr,form_data_),
						'assignees',array_to_json(array(select jsonb_agg(distinct users.usr_id) 
									  from cl_tx_sbtsk as sbtsk									    
									  left join cl_tx_users as users
									    on users.usr_id = any(sbtsk.assigned_to)
									 where sbtsk.srvc_req_id = srvc_req.db_id
									   and sbtsk.is_deleted is not true 
									 )),
						'srvc_req_contact_numbers',tms_hlpr_get_srvc_type_number_cust_fields(srvc_type.form_data,srvc_req.form_data :: json),
						'download_line_item_data',srvc_req.form_data->'line_items',
						'download_sp_line_item_data',srvc_req.form_data->'sp_line_items',
						'allow_site_attendance_download',allow_site_attendance_download,
						'allow_sp_daily_updates_download', allow_sp_daily_updates_download,
						'allow_sp_line_items_download', allow_sp_line_items_download,
						'allow_to_see_line_item_tab',allow_to_see_line_item_tab,
						'allow_to_add_sp_payout_tab',allow_to_add_sp_payout_tab,
						'allow_to_see_sp_payout_tab',allow_to_see_sp_payout_tab,
						'sp_payout_users_fr_vendors', user_list_fr_sp_vendors_,
						'is_sp_reassignment_allowed',is_sp_sbtsk_assigned,
						'created_date',DATE(((srvc_req.c_meta).time )::timestamp at time zone 'utc' at time zone 'Asia/kolkata'),
						'prvdr_srvc_hub', srvc_req.prvdr_srvc_hub,
						'prvdr_vertical', srvc_req.prvdr_vertical,
						'closed_date',DATE((closed_txn_log.trnstn_date)::timestamp at time zone 'utc' at time zone 'Asia/kolkata')
	    		   ) 
    		   
		      from public.cl_tx_srvc_req as srvc_req 
		     inner join cl_cf_srvc_statuses as srvc_status
		        on srvc_status.srvc_id = srvc_req.srvc_type_id 
		       and srvc_status.status_key = srvc_req.status
		     inner join cl_cf_service_types as srvc_type 
		        on srvc_type.service_type_id = srvc_req.srvc_type_id
		     inner join cl_tx_orgs as orgs_
		        on orgs_.org_id = srvc_type.org_id 
		      left join cl_cf_sbtsk_types as sbtsk_type
		        on (
			        	(
			        		is_prvdr = false 
			        		and
			        		sbtsk_type.sbtsk_type_id = any( array(SELECT json_array_elements_text(json_extract_path(srvc_type.form_data,'srvc_appl_sub_tasks'))) :: integer[] )
			           	)
			        or  (
			        		is_prvdr = true 
			        		and
			        		(		        			
			        			(
			        				cardinality(applicable_sbbtsk_types_) > 0		        				
				        			and
				        			sbtsk_type.sbtsk_type_id = any(applicable_sbbtsk_types_)	
			        			)		        			
			        			or
			        			(
			        				cardinality(applicable_sbbtsk_types_) = 0
			        				and
			        				sbtsk_type.org_id = org_id_	
			        			)		        			
			        		)		        		
			        	)
		        	)
		       and (sbtsk_type.sbtsk_type_id)::text not in (SELECT json_array_elements_text(dissallowed_sbtsk_types))
		      left join cl_tx_srvc_req_trnstn_log as closed_txn_log 
		      	on closed_txn_log.srvc_req_id = srvc_req.db_id
		  	   and closed_txn_log.status_key = 'closed'
			 where srvc_req.db_id = entry_id
			   and srvc_req.srvc_type_id = srvc_type_id_
			 group by srvc_type.service_type_id,srvc_status.db_id ,srvc_req.db_id, orgs_.org_id, closed_txn_log.db_id 
			 order by srvc_req.db_id desc 
		)
	);
	
	if json_array_length(resp_data) > 0 then 
		status = true;
		message = 'success';
	end if;

	return jsonb_build_object('status',status,'code',message,'data',resp_data->0);

end ;
$function$
;
