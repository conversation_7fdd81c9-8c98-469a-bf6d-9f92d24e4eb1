/* General Wrapper */
.booking-wrapper {
    padding: 20px;
   
}

/* Header */
.booking-label {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.booking-sub-label {
    font-size: 14px;
    color: #666;
}

.week-selector-container {
    width: 300px;
    background-color: #e3e4e9;
    border-radius: 4px;
    padding: 2px;
}

/* Card Scroll */
.booking-scroll-card .ant-card-body {
    padding: 12px;
}

.booking-scroll-container {
    display: flex;
    gap: 16px;
    min-height: 100%;
    overflow-x: auto;
}

.booking-day-card {
    min-width:380px;
    background-color: #6AB8E4;
    border-radius: 8px;
    padding: 12px;
    color: white;
    flex-shrink: 1;
}

.booking-day-header {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 12px;
    text-align: center;
}

.slot-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.slot-box {
    background-color: white;
    color: #333;
    padding: 6px 4px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #ddd;
    transition: all 0.2s ease;
    position: relative;
    width: 48%;
}

.slot-box-selected {
    background-color: #2196F3;
    color: white;
    border: 2px solid #1976D2;
}

.slot-label-tag {
    position: absolute;
    bottom: 2px;
    left: 2px;
    font-size: 9px;
    background-color: #FF9800;
    color: white;
    padding: 0px 3px;
    border-radius: 2px;
}

.selected-slot-summary {
    margin-top: 24px;
}

.selected-slot-card {
    /* background-color: #99e999; */
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    /* color: white; */
    font-weight: 500;
}

.selected-slot-day {
    margin-bottom: 6px;
    font-size: 15px;
}

.selected-slot-divider {
    border-color: white;
    opacity: 0.3;
}

.selected-slot-tags {
    margin-top: 6px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.selected-slot-tag {
    font-size: 13px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #e0f7e9;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
    font-weight: 600;
}