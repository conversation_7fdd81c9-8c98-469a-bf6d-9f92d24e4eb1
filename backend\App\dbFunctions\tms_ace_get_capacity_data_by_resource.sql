DROP FUNCTION IF EXISTS public.tms_ace_get_capacity_data_by_resource(integer, integer, integer, text, text);

-- Create function
CREATE OR REPLACE FUNCTION public.tms_ace_get_capacity_data_by_resource(
    p_org_id integer,
    p_vertical_id integer,
    p_skill_id integer,
    p_hub_id text,
    p_date_utc text DEFAULT NULL
)
RETURNS TABLE (
    resource_id text,
    provider_id integer,
    vertical_id integer,
    vertical_name text,
    skill_id integer,
    skill_name varchar(20),
    hub_id bigint,
    hub_name varchar(100),
    hub_code varchar(15),
    start_time timestamp with time zone,
    end_time timestamp with time zone,
    total_capacity integer,
    available_capacity integer,
    booked_capacity numeric
)
LANGUAGE plpgsql AS $function$
-- Declarations
DECLARE
    -- Timestamps and IDs
    current_time_utc timestamp with time zone;
    hub_id_bigint bigint;
BEGIN
    -- Convert hub_id to bigint with error handling
    hub_id_bigint := p_hub_id::bigint;

    -- Check if the organization exists
    IF NOT EXISTS (SELECT 1 FROM public.cl_tx_orgs WHERE org_id = p_org_id) THEN
        RAISE EXCEPTION 'Organization not found';
    END IF;

    -- Check if capacity module is enabled for the organization
    IF NOT EXISTS (
        SELECT 1
        FROM public.cl_tx_orgs_settings
        WHERE org_id = p_org_id
        AND settings_type = 'ACE_CAPACITY_SETTINGS'
        AND (settings_data->>'enable_capacity_module')::boolean = true
    ) THEN
        RAISE EXCEPTION 'Capacity module is not enabled for this organization';
    END IF;

    -- Get time slots from the vertical time slots tables
    -- Use the provided date or default to tomorrow (UTC)
    IF p_date_utc IS NULL THEN
        -- Default to tomorrow at midnight UTC
        current_time_utc := (CURRENT_DATE + INTERVAL '1 day') AT TIME ZONE 'UTC';
    ELSE
        -- Use the provided date string as is (at midnight UTC)
        current_time_utc := (p_date_utc || ' 00:00:00')::timestamp AT TIME ZONE 'UTC';
    END IF;

    -- Get time slots for the resource
    RETURN QUERY
    WITH slots AS (
        SELECT ts.slot_id,
               ts.start_time AS slot_start_time,
               ts.end_time AS slot_end_time,
               ts.is_active,
               (current_time_utc::date + ts.start_time) AT TIME ZONE 'UTC' AS actual_start_time,
               (current_time_utc::date + ts.end_time) AT TIME ZONE 'UTC' AS actual_end_time
          FROM tms_hlpr_get_time_slots_for_resource(p_org_id, p_vertical_id, p_skill_id, hub_id_bigint, current_time_utc) ts
    )
    -- Final result with direct joins instead of CTEs
    SELECT p_org_id::text || '_' || v.db_id::text || '_' || s.db_id::text || '_' || h.id::text AS resource_id,
           p_org_id AS provider_id,
           v.db_id AS vertical_id,
           v.settings_data->>'vertical_title' AS vertical_name,
           s.db_id AS skill_id,
           s.skill_name AS skill_name,
           h.id AS hub_id,
           h.hub_name AS hub_name,
           h.hub_code AS hub_code,
           sl.actual_start_time AS start_time,
           sl.actual_end_time AS end_time,
           COUNT(u.usr_id)::integer AS total_capacity,
           CEIL(SUM(
               CASE WHEN u.usr_id IS NOT NULL THEN
                   tms_hlpr_calculate_user_availability(
                       u.usr_id,
                       p_org_id,
                       sl.actual_start_time,
                       sl.actual_end_time
                   )
               ELSE 0
               END
           ))::integer AS available_capacity,
           COALESCE(SUM(
               CASE WHEN sb.db_id IS NOT NULL THEN
                   tms_hlpr_calculate_booked_capacity_overlap(
                       sb.start_time,
                       sb.end_time,
                       sl.actual_start_time,
                       sl.actual_end_time
                   )
               ELSE 0
               END
           ), 0)::numeric AS booked_capacity -- Calculate booked capacity as decimal based on time overlap using helper function
      FROM slots sl
    -- Vertical information (direct join)
      JOIN public.cl_tx_orgs_settings v
        ON v.org_id = p_org_id
       AND v.settings_type = 'SP_CUSTOM_FIELDS'
       AND v.db_id = p_vertical_id
    -- Skill information (direct join)
      JOIN public.cl_tx_skills s
        ON s.db_id = p_skill_id
       AND s.isactive = true
      JOIN public.cl_tx_skill_map sm
        ON s.db_id = sm.skill_id
       AND sm.vertical_id = p_vertical_id
    -- Hub information (direct join)
      JOIN public.cl_tx_vertical_srvc_hubs h
        ON h.org_id = p_org_id
       AND h.vertical_id = p_vertical_id
       AND h.id = hub_id_bigint
       AND h.is_active = true
    -- Users for this resource (left join)
 LEFT JOIN public.cl_tx_users u
        ON u.org_id = p_org_id
       AND u.is_active = true
       AND u.primary_vertical = p_vertical_id
       AND u.primary_srvc_hub = hub_id_bigint
       AND (u.primary_skill1 = p_skill_id OR
            u.primary_skill2 = p_skill_id OR
            u.primary_skill3 = p_skill_id)
    -- Bookings (left join)
 LEFT JOIN public.cl_tx_sbtsk sb
        ON u.usr_id = ANY(sb.assigned_to)
       AND sb.start_time < sl.actual_end_time
       AND sb.end_time > sl.actual_start_time
       AND sb.is_deleted IS NOT TRUE
       AND sb.org_id = p_org_id
 GROUP BY v.db_id,
          v.settings_data->>'vertical_title',
          s.db_id,
          s.skill_name,
          h.id,
          h.hub_name,
          h.hub_code,
          sl.slot_id,
          sl.slot_start_time,
          sl.slot_end_time,
          sl.actual_start_time,
          sl.actual_end_time
 ORDER BY sl.slot_start_time ASC;
END;
$function$;
