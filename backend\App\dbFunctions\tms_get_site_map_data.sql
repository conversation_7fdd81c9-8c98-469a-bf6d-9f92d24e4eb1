CREATE OR REPLACE FUNCTION public.tms_get_site_map_data(requester_info json, filters_ json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare 
	status boolean;
	message text;

--temp
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;

--Output
	total integer;

--filters
	filter_creation_date text[];
	filter_search_query text;
	resp_data json;
	visit_map_data json;
	onfield_subtask_types integer[];
	filter_technician uuid[];

begin
	status = false;
	message = 'Internal_error';

    -- org_id_ = json_extract_path_text(requester_info,'org_id');
    -- usr_id_ = json_extract_path_text(requester_info,'usr_id');
    -- ip_address_ = json_extract_path_text(requester_info,'ip_address');
    -- user_agent_ = json_extract_path_text(requester_info,'user_agent');
	-- set srvc_type_id in requester info
	requester_info = jsonb_set(requester_info::jsonb, '{srvc_type_id}', '0'::jsonb);

	-- Check if creation_date is present in filters
	-- if not then set todays day as from and next day as to date
	filter_creation_date = array( select json_array_elements_text(json_extract_path(filters_,'creation_date')) )::text[];
	if cardinality(filter_creation_date) = 0 then
		filter_creation_date = array[DATE(now())::text, DATE(now() + interval '1 day')::text];
		filters_ = jsonb_set(filters_::jsonb, '{creation_date}', to_json(filter_creation_date)::jsonb);
	end if;

	-- set srvc_status_category as array of 'unclosed', 'CLOSED'

	filters_ = jsonb_set(filters_::jsonb, '{srvc_status_category}', '["unclosed","CLOSED"]'::jsonb);

	select tms_get_srvc_reqs_by_filter_as_array_v2(requester_info,1 , 50000, filters_, '')
	  into matching_ids_json;

   	-- raise notice 'matching_ids_json %', matching_ids_json;
	SELECT array_agg((value->>'id')::int) 
	  FROM json_array_elements(matching_ids_json) AS value
	  into matching_ids;
	 -- json_array_elements
--	FOR single_id_index IN 0..json_array_length(matching_ids_json) - 1 loop
--	  temp_id = json_extract_path_text(matching_ids_json -> single_id_index,'id');
--	  total = json_extract_path_text(matching_ids_json -> single_id_index,'full_count');
--	 	
--      matching_ids = array_append(matching_ids, temp_id);
--    END LOOP;
   
	-- raise notice 'Matching ids %', matching_ids;

	visit_map_data = array_to_json(array(
    	select jsonb_build_object(
    				'site_id', srvc_req.db_id ,
    				'site_name', srvc_req.display_code ,
					'geocoding_location_data', srvc_req.form_data->'geocoding_location_data',
					'pincode', srvc_req.cust_pincode
			   )  
	      from cl_tx_srvc_req as srvc_req
		 where srvc_req.db_id = any( matching_ids )
	));	

	resp_data =  jsonb_build_object('data',visit_map_data);
	
--sending the below by default
    status = true;
    message = 'success';

	return json_build_object('status',status,'code',message,'data',resp_data);

END;
$function$
;
