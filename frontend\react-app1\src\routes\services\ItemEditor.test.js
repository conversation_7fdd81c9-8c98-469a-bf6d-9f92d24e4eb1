import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import ItemEditor from './ItemEditor';
import ConfigHelpers from '../../util/ConfigHelpers';
import http_utils from '../../util/http_utils';
import userEvent from '@testing-library/user-event';
import { isMobileView, isScreenZoomPercentage125 } from '../../util/helpers';
import {
    initPropsFrPandLConfig,
    rerenderPropsFrPandLConfig,
    SPviewDataFrDisabledPL,
    viewDataFrEnabledPLWithRolesSP,
    viewDataFrEnabledPLWithNoRolesSP,
    viewDataFrBrand,
    fakeProps,
    fakeViewData1,
    fakeViewData2,
    fakeViewData3,
    viewDataFrDisabledPLProjectSP,
    viewDataForProjectEnbaledPLWithAuthRoleSP,
} from './ItemEditor.test.data';
import ProfitAndLoss from './ProfitAndLoss';

//mock configHelpers
jest.mock('../../util/ConfigHelpers');
jest.mock('../../util/http_utils');

//mock LocationSearchInput
jest.mock('../../components/LocationSearchInput', () => () => (
    <div>LocationSearchInput</div>
));
//mock SrvcReqCalendar
jest.mock('./Calendar', () => () => <div>SrvcReqCalendar</div>);
jest.mock('./ProfitAndLoss', () => () => <div>ProfitAndLoss</div>);
beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});
afterEach(() => {
    jest.clearAllMocks();
});

// Test data for capacity API
const mockCapacityData = [
    {
        category_value: 'electronics',
        category_label: 'Electronics',
        slots: [
            {
                start: '2025-04-01T09:00:00Z',
                end: '2025-04-01T10:00:00Z',
                label: '9 AM - 10 AM',
                available_qty: 5,
            },
        ],
    },
];

const mockSpConfigData = {
    settings_data: {
        db: {
            id: 123, // vertical_id
        },
    },
};

const viewData = {
    statuses: [
        {
            color: '#e91e63',
            label: 'Open',
            title: 'Open',
            value: 'open',
        },
        {
            color: '#03a9f4',
            label: 'Assigned',
            title: 'Assigned',
            value: 'wo29HfLp',
        },
        {
            color: '#009688',
            label: 'Qualified',
            title: 'Qualified',
            value: 'LtOXOoSa',
        },
        {
            color: '#ffc107',
            label: 'Proposals',
            title: 'Proposals',
            value: 'e856B5rk',
        },
        {
            color: '#4caf50',
            label: 'Customer',
            title: 'Customer',
            value: 'rHiId3iY',
        },
        {
            color: '#607d8b',
            label: 'Closed',
            title: 'Closed',
            value: 'closed',
        },
    ],
    role_list: [
        {
            label: 'Admin',
            value: 3,
        },
        {
            label: 'Customer service',
            value: 115,
        },
        {
            label: 'Sales',
            value: 117,
        },
        {
            label: 'Technician',
            value: 118,
        },
    ],
    srvc_type_id: 16,
    sp_config_data: [],
    role_list_vs_users: {
        3: [
            {
                label: 'Abhishek',
                value: 'b3d9310f-e098-49de-a9ba-e23805994f60',
                role_id: 3,
            },
            {
                label: 'Abhishek1',
                value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                role_id: 3,
            },
            {
                label: 'Demo User',
                value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                role_id: 3,
            },
            {
                label: 'Gaurav Pangam',
                value: '315efff0-a733-40c0-bc1a-fa4b1dc043b3',
                role_id: 3,
            },
            {
                label: 'Shambhu Choudhary',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                role_id: 3,
            },
            {
                label: 'Test 1',
                value: '169986d3-19cc-4dd5-99bd-b2b56bca8b65',
                role_id: 3,
            },
            {
                label: 'Test 2',
                value: '94322d02-1c99-4653-b0ed-c1b0aa5ad45c',
                role_id: 3,
            },
            {
                label: 'Test 3',
                value: '630a4a26-2f41-4cbc-8242-f32d8d5f967d',
                role_id: 3,
            },
        ],
        115: [
            {
                label: 'Abhishek1',
                value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                role_id: 115,
            },
            {
                label: 'Balwant Kumar',
                value: '75490a94-f1de-40f8-a6a9-8ec2481bf493',
                role_id: 115,
            },
            {
                label: 'Demo User',
                value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                role_id: 115,
            },
            {
                label: 'Jainsih',
                value: '065dd489-701f-4a0a-8656-482988595594',
                role_id: 115,
            },
            {
                label: 'Lovnish Bhatia',
                value: 'dcfd349d-4c33-4c2e-90b2-88d1570ecfc4',
                role_id: 115,
            },
            {
                label: 'Shambhu Choudhary',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                role_id: 115,
            },
            {
                label: 'suraj',
                value: '99952844-dcac-4392-918f-19b3e13e8532',
                role_id: 115,
            },
        ],
        117: [
            {
                label: 'Navin',
                value: 'ca694780-060f-4ba8-97d5-4d3a352459a3',
                role_id: 117,
            },
        ],
        118: [
            {
                label: 'Mandeep Singh',
                value: '8f9f0a3e-c032-4847-bf73-8effff335250',
                role_id: 118,
            },
            {
                label: 'Manoj Shinde',
                value: '9576d268-7a8c-42f9-8c4a-f6acd957e5da',
                role_id: 118,
            },
        ],
    },
    sp_authorities_config_data: {
        org_id: 2,
        usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
        entry_id: 3,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_id: [2, 140, 145],
        sbtsk_fr_145: 4,
        sbtsk_fr_146: 13,
        sbtsk_fr_147: 10,
        srvc_type_id: [57, 16],
        vertical_desc: 'TEst',
        sp_authority_2: 140,
        vertical_title: 'LF',
        rating_type_145: 'another_authority',
        vertical_nature: 'project_based',
        enable_sp_rating: true,
        sp_authority_140: 2,
        sp_authority_145: 2,
        sp_rating_type_2: 'another_authority',
        sp_rating_type_140: 'static_user',
        sp_rating_type_145: 'another_authority',
        sp_rating_type_146: 'static_user',
        sp_static_user_140: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_cust_fields_json:
            '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
        sp_rating_template_2: 3,
        sp_rating_template_140: 3,
        sp_rating_template_145: 7,
        srvc_type_enable_billing: true,
        deployment_possible_roles: [145, 146, 147],
        sp_deployment_who_can_edit: [],
        sp_authority_fr_deployment_145: '',
        sp_authority_fr_deployment_146: 2,
        sp_authority_fr_deployment_147: 2,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '9:00AM',
        deployment_time_slot_upper_limit: '7:00PM',
        sp_rating_type_fr_deployment_145: 'static_user',
        sp_rating_type_fr_deployment_146: 'static_user',
        sp_rating_type_fr_deployment_147: 'authority',
        sp_static_user_fr_deployment_145: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_146: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_147: {
            key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            label: 'User 2',
            value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
        },
        sp_rating_template_fr_deployment145: 7,
        sp_rating_template_fr_deployment146: 7,
        sp_rating_template_fr_deployment147: 3,
        '2_enable_cross_visibility_of_authorities': true,
        '140_enable_cross_visibility_of_authorities': true,
        '145_enable_cross_visibility_of_authorities': true,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
    },
    role_wise_authorities_users_list: [
        {
            key: 'authority_3',
            label: 'Admin',
            widget: 'select',
            options: [
                {
                    label: 'Abhishek',
                    value: 'b3d9310f-e098-49de-a9ba-e23805994f60',
                    role_id: 3,
                },
                {
                    label: 'Abhishek1',
                    value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                    role_id: 3,
                },
                {
                    label: 'Demo User',
                    value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                    role_id: 3,
                },
                {
                    label: 'Gaurav Pangam',
                    value: '315efff0-a733-40c0-bc1a-fa4b1dc043b3',
                    role_id: 3,
                },
                {
                    label: 'Shambhu Choudhary',
                    value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                    role_id: 3,
                },
                {
                    label: 'Test 1',
                    value: '169986d3-19cc-4dd5-99bd-b2b56bca8b65',
                    role_id: 3,
                },
                {
                    label: 'Test 2',
                    value: '94322d02-1c99-4653-b0ed-c1b0aa5ad45c',
                    role_id: 3,
                },
                {
                    label: 'Test 3',
                    value: '630a4a26-2f41-4cbc-8242-f32d8d5f967d',
                    role_id: 3,
                },
            ],
            role_id: 3,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
        {
            key: 'authority_115',
            label: 'Customer service',
            widget: 'select',
            options: [
                {
                    label: 'Abhishek1',
                    value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                    role_id: 115,
                },
                {
                    label: 'Balwant Kumar',
                    value: '75490a94-f1de-40f8-a6a9-8ec2481bf493',
                    role_id: 115,
                },
                {
                    label: 'Demo User',
                    value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                    role_id: 115,
                },
                {
                    label: 'Jainsih',
                    value: '065dd489-701f-4a0a-8656-482988595594',
                    role_id: 115,
                },
                {
                    label: 'Lovnish Bhatia',
                    value: 'dcfd349d-4c33-4c2e-90b2-88d1570ecfc4',
                    role_id: 115,
                },
                {
                    label: 'Shambhu Choudhary',
                    value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                    role_id: 115,
                },
                {
                    label: 'suraj',
                    value: '99952844-dcac-4392-918f-19b3e13e8532',
                    role_id: 115,
                },
            ],
            role_id: 115,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
        {
            key: 'authority_117',
            label: 'Sales',
            widget: 'select',
            options: [
                {
                    label: 'Navin',
                    value: 'ca694780-060f-4ba8-97d5-4d3a352459a3',
                    role_id: 117,
                },
            ],
            role_id: 117,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
        {
            key: 'authority_118',
            label: 'Technician',
            widget: 'select',
            options: [
                {
                    label: 'Mandeep Singh',
                    value: '8f9f0a3e-c032-4847-bf73-8effff335250',
                    role_id: 118,
                },
                {
                    label: 'Manoj Shinde',
                    value: '9576d268-7a8c-42f9-8c4a-f6acd957e5da',
                    role_id: 118,
                },
            ],
            role_id: 118,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
    ],
    srvc_prvdr_role_wise_authorities_users_list: [],
    form_data: {
        id: 516,
        title: 'HMLL240724397692',
        org_id: 3,
        status: {
            key: 'rHiId3iY',
            color: '#4caf50',
            title: 'Customer',
            status_type: 'DONE',
        },
        priority: 'Normal',
        assignees: [null],
        form_data: {
            title: 'Failed to send SMS',
            host_d: 'localhost:3000',
            org_id: 3,
            usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            comment:
                ' Consumer Number/Technician masked Number/Subtask not found.',
            entry_id: 516,
            cust_city: 'Solapur',
            mic_files: {},
            new_prvdr: '2',
            cust_email: null,
            cust_state: 'MAHARASHTRA',
            ip_address: '::1',
            new_status: 'rHiId3iY',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            attachments: {},
            authority_2: '177886be-ba77-410a-82b2-deb762b8c1c4',
            authority_3: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
            cust_line_0: null,
            cust_line_1: null,
            cust_line_2: null,
            cust_line_3: null,
            cust_mobile: '7972083054',
            camera_files: {},
            cust_pincode: '413005',
            srvc_type_id: '16',
            authority_115: '75490a94-f1de-40f8-a6a9-8ec2481bf493',
            authority_117: '',
            authority_118: '',
            authority_140: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            authority_145: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            cust_full_name: 'Prathmesh Chiman',
            is_frm_frontend: true,
            request_priority: 'Normal',
            notification_data: true,
            is_customer_access: 0,
            update_for_comment: false,
            request_description: 'rating 4',
            srvc_type_his_db_id: '335',
            prvdr_assg_timestamp: '2024-07-24T10:13:04.390605',
            sp_srvc_type_his_db_id: '268',
            geocoding_location_data: {
                location: {
                    lat: 17.6748607,
                    lng: 75.95213,
                },
                location_type: 'APPROXIMATE',
            },
            lastChangedFileSectionIds: [],
        },
        is_deleted: false,
        all_ratings: [],
        sbtsk_types: [
            {
                icon: 'icon-feedback',
                label: 'Simple task',
                value: '3',
            },
        ],
        srvc_type_id: 16,
        revisions_data: [],
        sp_all_ratings: [],
        sp_config_data: [null],
        srvc_type_title: 'Leads',
        location_grp_ids: [33],
        sp_revisions_data: [],
        subtaskConfigData: [],
        status_transitions: [
            {
                id: 674,
                key: 'open',
                c_by: 'Shambhu Choudhary',
                time: '2024-07-24T10:13:04.390605',
                u_by: null,
                c_meta: {
                    time: '2024-07-24T10:13:04.390605',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: null,
                details: {
                    color: '#e91e63',
                    title: 'Open',
                    status: 'open',
                },
            },
            {
                id: 677,
                key: 'e856B5rk',
                c_by: 'Gaurav Pangam wify',
                time: '2024-09-16T08:28:31.518123',
                u_by: 'Shambhu Choudhary',
                c_meta: {
                    time: '2024-07-24T10:36:37.639527',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: {
                    time: '2024-09-16T08:28:31.518123',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                details: {
                    color: '#ffc107',
                    title: 'Proposals',
                    status: 'e856B5rk',
                },
            },
            {
                id: 675,
                key: 'wo29HfLp',
                c_by: 'Shambhu Choudhary',
                time: '2024-09-16T08:28:57.440604',
                u_by: 'Shambhu Choudhary',
                c_meta: {
                    time: '2024-07-24T10:13:04.969972',
                    ip_addr: null,
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: {
                    time: '2024-09-16T08:28:57.440604',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                details: {
                    color: '#03a9f4',
                    title: 'Assigned',
                    status: 'wo29HfLp',
                },
            },
            {
                id: 678,
                key: 'LtOXOoSa',
                c_by: 'Shambhu Choudhary',
                time: '2024-09-16T08:30:00.538657',
                u_by: 'Shambhu Choudhary',
                c_meta: {
                    time: '2024-09-16T08:12:35.285594',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: {
                    time: '2024-09-16T08:30:00.538657',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                details: {
                    color: '#009688',
                    title: 'Qualified',
                    status: 'LtOXOoSa',
                },
            },
            {
                id: 676,
                key: 'rHiId3iY',
                c_by: 'Gaurav Pangam wify',
                time: '2024-09-16T08:30:27.671529',
                u_by: 'Shambhu Choudhary',
                c_meta: {
                    time: '2024-07-24T10:34:42.082178',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: {
                    time: '2024-09-16T08:30:27.671529',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                details: {
                    color: '#4caf50',
                    title: 'Customer',
                    status: 'rHiId3iY',
                },
            },
        ],
        billing_final_amount: 0,
        download_line_item_data: null,
        srvc_req_locked_by_name: null,
        send_for_billing_by_name: null,
        srvc_req_contact_numbers: [
            {
                key: 'cust_mobile',
                label: 'Primary Number',
                value: '7972083054',
            },
        ],
        discount_approved_by_name: null,
        allow_to_see_line_item_tab: true,
        download_sp_line_item_data: null,
        sp_srvc_req_locked_by_name: null,
        sp_send_for_billing_by_name: null,
        allow_sp_line_items_download: true,
        sp_discount_approved_by_name: null,
        allow_site_attendance_download: true,
        allow_sp_daily_updates_download: true,
        srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday: null,
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        sp_srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday: null,
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        is_enable_auto_assign_authorities_refresh_btn: false,
    },
};
const props = {
    showEditor: true,
    editMode: true,
    editorItem: {
        id: 516,
        org: {
            label: 'Homelane',
            value: 3,
            icon_path: 'https://super.homelane.com/hlico4.ico',
        },
        addr: '413005, Solapur, MAHARASHTRA',
        time: '2024-07-24T10:13:04.390605',
        title: 'HMLL240724397692',
        c_time: '2024-07-24T10:13:04.390605',
        labels: null,
        status: {
            key: 'rHiId3iY',
            color: '#4caf50',
            title: 'Customer',
            status_type: 'DONE',
            transition_date: '2024-09-16T08:30:27.671529',
        },
        priority: 'Normal',
        req_date: null,
        c_by_name: 'Shambhu Choudhary',
        cust_city: 'Solapur',
        cust_name: 'Prathmesh Chiman',
        is_deleted: false,
        srvc_prvdr: '2',
        description: 'rating 4',
        sbtsks_meta: null,
        cust_pincode: '413005',
        ext_order_id: null,
        req_end_time: null,
        srvc_type_id: 16,
        feedback_data: null,
        req_start_time: null,
        Attachment_count: null,
        brand_location_groups: 'Maharashtra',
        fields_data_for_table: [
            {
                attachments: {},
            },
            {
                cust_full_name: 'Prathmesh Chiman',
            },
            {
                request_priority: 'Normal',
            },
            {
                request_description: 'rating 4',
            },
        ],
        prvdr_location_groups: 'MH',
        geocoding_location_data: {
            location: {
                lat: 17.6748607,
                lng: 75.95213,
            },
            location_type: 'APPROXIMATE',
        },
    },
    isCustomerRequests: false,
    srvcConfigData: {
        qty: '',
        rate: '',
        total: '',
        org_id: 3,
        usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        entry_id: 16,
        ip_address: '::1',
        sbtsk_fr_3: 2,
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_3: 115,
        sbtsk_fr_115: 2,
        sbtsk_fr_117: 2,
        sbtsk_fr_118: 3,
        static_user3: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        authority_115: 3,
        enable_rating: true,
        rating_type_3: 'static_user',
        srvc_statuses:
            '{"ACTIVE":[{"key":"open","color":"#e91e63","title":"Open","status_type":"ACTIVE"},{"key":"wo29HfLp","color":"#03a9f4","title":"Assigned","status_type":"ACTIVE"},{"key":"LtOXOoSa","color":"#009688","title":"Qualified","status_type":"ACTIVE"}],"DONE":[{"key":"e856B5rk","color":"#ffc107","title":"Proposals","status_type":"DONE"},{"key":"rHiId3iY","color":"#4caf50","title":"Customer","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#607d8b","title":"Closed","status_type":"CLOSED"}]}',
        srvc_type_key: 'leads',
        srvc_type_desc: 'Potential sales lead',
        srvc_type_name: 'Leads',
        static_user117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        static_user118: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        rating_type_115: 'another_authority',
        rating_type_117: 'static_user',
        rating_type_118: 'static_user',
        srvc_authorities: [3, 115, 117, 118],
        srvc_type_nature: 'project_based',
        srvc_type_prefix: 'HMLL',
        rating_template_3: 6,
        srvc_can_cust_rate: true,
        rating_template_115: 2,
        rating_template_117: 2,
        rating_template_118: 5,
        srvc_appl_sub_tasks: [3],
        show_line_items_to_sp: true,
        srvc_category_field: '577da6e2-7e6b-4e37-8bc7-0736c8bde7fa',
        srvc_possible_prvdrs: [2, 16],
        srvc_cust_fields_json:
            '{"originalFields":[{"id":"b78c6f47-38d4-49d8-bde9-aeb422357210","element":"TextInput","required":false,"label":{"blocks":[{"key":"811ba","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","element":"Files","label":{"blocks":[{"key":"28mkc","text":"Upload Selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","element":"Files","label":{"blocks":[{"key":"ciq9s","text":"upload file","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"71193b52-8ea9-496f-88c4-7feff9f6324e","element":"WIFY_CAMERA","label":{"blocks":[{"key":"19i3p","text":"Take selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","element":"NumberInput","required":false,"label":{"blocks":[{"key":"bc36h","text":"Number","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"48ff354a-2824-4fff-9345-34285605f38f","element":"Tags","required":false,"label":{"blocks":[{"key":"dmrs3","text":"Drop Down","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"d1f5da3e-7b7e-4404-912e-a2415eb08e88","label":"Label1","value":"Value1"},{"id":"425c1cc7-0a13-49f7-bdf5-503766302d5a","label":"Label2","value":"Value2"}]},{"id":"051bbed3-fa45-4822-b11c-f852afd7d7d0","element":"Dropdown","label":{"blocks":[{"key":"6868p","text":"cat1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"2c94a04c-013a-4984-b687-abd1c7e7b943","value":"Option1"},{"id":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d","value":"Option2"}]},{"id":"501499f2-8d57-430c-a3d6-99e88c0c6905","element":"Tags","required":false,"label":{"blocks":[{"key":"dlc9g","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"04534439-8c55-4e3e-b1d0-bf1e9b973b90","label":"Label1","value":"Value1"},{"id":"c5d05b33-ea66-4012-b654-f79d2339be42","label":"Label2","value":"Value2"}]},{"id":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","element":"Dropdown","label":{"blocks":[{"key":"5if67","text":"cat 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"7e2fb8e4-5939-43da-9250-840f5c76110e","value":"Option1"},{"id":"0fd9fea2-c3f6-40f3-9385-bf085cb53175","value":"Option2"}]},{"id":"383d429b-7bdc-4f1c-83d7-098992bdefb9","element":"WIFY_BARCODE_SCANNER","label":{"blocks":[{"key":"7r0fd","text":"Barcode","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"b78c6f47-38d4-49d8-bde9-aeb422357210","required":false,"label":"Placeholder Label"},{"key":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","required":false,"label":"Upload Selfie","cust_component":"Files","cust_component_value":""},{"key":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","required":false,"label":"upload file","cust_component":"Files","cust_component_value":""},{"key":"71193b52-8ea9-496f-88c4-7feff9f6324e","required":false,"label":"Take selfie","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","required":false,"label":"Number","widget":"number"},{"key":"48ff354a-2824-4fff-9345-34285605f38f","required":false,"label":"Drop Down","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"051bbed3-fa45-4822-b11c-f852afd7d7d0","required":false,"label":"cat1","widget":"select","options":[{"label":"Option1","value":"2c94a04c-013a-4984-b687-abd1c7e7b943"},{"label":"Option2","value":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d"}]},{"key":"501499f2-8d57-430c-a3d6-99e88c0c6905","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","required":false,"label":"cat 2","widget":"select","options":[{"label":"Option1","value":"7e2fb8e4-5939-43da-9250-840f5c76110e"},{"label":"Option2","value":"0fd9fea2-c3f6-40f3-9385-bf085cb53175"}]},{"key":"383d429b-7bdc-4f1c-83d7-098992bdefb9","required":false,"label":"Barcode","cust_component":"WIFY_BARCODE_SCANNER","cust_component_value":""}]}',
        srvc_default_provider: 2,
        srvc_enable_srvc_prvdr: true,
        srvc_type_billing_type: 'hybrid',
        deployment_who_can_edit: [],
        srvc_type_icon_selector: 'icon-revenue-new',
        srvc_type_enable_billing: true,
        authority_fr_deployment_3: 118,
        daily_update_who_can_edit: [115, 3],
        deployment_possible_roles: [118, 117, 3, 115],
        srvc_type_line_item_config:
            '{"7afd88b5-d5b5-46d6-b132-8af606f07390":{"key":"7afd88b5-d5b5-46d6-b132-8af606f07390","label":"Installation"}}',
        authority_fr_deployment_118: 3,
        dont_send_srvc_feedback_sms: true,
        rating_type_fr_deployment_3: 'authority',
        srvc_cust_pincode_mandatory: true,
        srvc_is_cust_fields_dynamic: false,
        daily_update_will_have_issues: 'Yes',
        rating_type_fr_deployment_117: 'static_user',
        rating_type_fr_deployment_118: 'authority',
        static_user_fr_deployment_117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        daily_update_issue_form_fields:
            '{"originalFields":[{"id":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","element":"Files","label":{"blocks":[{"key":"538a9","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","element":"Dropdown","label":{"blocks":[{"key":"51rtb","text":"single","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"f73c8d87-9175-47a6-8682-03d76e10d8fe","value":"Option1"},{"id":"8486c4d8-885c-41da-a703-a77e47bc6bb1","value":"Option2"}]},{"id":"884742c5-da33-408b-aa44-8a524d16a94b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"1v142","text":"Radio button","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f","label":"Label1","value":"Value1","checked":false},{"id":"eea282d8-bd39-46df-8893-90ee46f29500","label":"Label2","value":"Value2","checked":false}]}],"translatedFields":[{"key":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","required":false,"label":"Placeholder Label","cust_component":"Files","cust_component_value":""},{"key":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","required":false,"label":"single","widget":"select","options":[{"label":"Option1","value":"f73c8d87-9175-47a6-8682-03d76e10d8fe"},{"label":"Option2","value":"8486c4d8-885c-41da-a703-a77e47bc6bb1"}]},{"key":"884742c5-da33-408b-aa44-8a524d16a94b","required":false,"label":"Radio button","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f"},{"label":"Label2","value":"eea282d8-bd39-46df-8893-90ee46f29500"}]}]}',
        srvc_feedback_send_by_whatsapp: true,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        '3_auto_assign_based_on_location': false,
        rating_template_fr_deployment_3: 2,
        srvc_req_cust_pincode_mandatory: false,
        deployment_time_slot_lower_limit: '12:00AM',
        deployment_time_slot_upper_limit: '11:45PM',
        srvc_authority_3_specific_fields: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
        ],
        srvc_feedback_trigger_status_key: 'closed',
        '115_auto_assign_based_on_location': false,
        '117_auto_assign_based_on_location': false,
        '118_auto_assign_based_on_location': false,
        rating_template_fr_deployment_117: 5,
        rating_template_fr_deployment_118: 2,
        select_whatsApp_feedback_template:
            'Dear %cust_name%, Your %display_code% request: %display_code% has been registered. Kindly connect our Welspun customer care %feedback_link% if need help.',
        srvc_authority_115_specific_fields: [],
        srvc_auto_movement_fr_LtOXOoSa_key: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        srvc_auto_movement_fr_e856B5rk_key: [
            '48ff354a-2824-4fff-9345-34285605f38f',
            'eff1a08f-151f-4590-bf71-8cbd4ea6e515',
        ],
        srvc_auto_movement_fr_wo29HfLp_key: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
            '04867f4b-1d13-4020-b2c8-d3a53e38bd45',
        ],
        srvc_type_enable_additional_billing: true,
        srvc_type_pricing_config_for_manday: '{}',
        srvc_type_who_can_sync_srvc_req_prc: [3],
        srvc_type_enable_billing_discounting: true,
        daily_update_track_line_item_progress: 'Yes',
        srvc_enable_srvc_prvdr_to_add_requests: true,
        srvc_type_who_can_send_req_for_billing: [3],
        cnsmr_ntfctn_fr_status_open_sms_enabled: true,
        srvc_type_status_open_notify_authorties: [3, 115],
        '3_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_open_sms_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        auto_status_change_fr_on_field_status_key: 'open',
        daily_update_dynamic_line_item_wise_files: 'Yes',
        '115_enable_cross_visibility_of_authorities': true,
        '117_enable_cross_visibility_of_authorities': true,
        '118_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_enabled: false,
        cnsmr_ntfctn_fr_status_open_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_enabled: true,
        srvc_type_who_can_lock_srvc_req_for_billing: [3],
        srvc_type_who_will_get_notified_for_billing: [3],
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_template:
            'Dears %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_template:
            'Dear %cust_name%, Pureit technician %technician_name% is assigned to you for your service request 1234. Contact: %technician_masked_number%, PIN: %masked_number_pin%.',
        srvc_authority_based_movement_status_LtOXOoSa: [],
        srvc_authority_based_movement_status_e856B5rk: [117],
        srvc_authority_based_movement_status_wo29HfLp: [3, 115],
        '3_show_authorities_that_report_to_the_assigner': false,
        hide_authority_selection_from_specific_details: false,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_status_type: 'schedule_assignment',
        srvc_type_categorize_specific_fields_for_billing: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        cnsmr_ntfctn_fr_status_open_whatsapp_message_enabled: false,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_status_type:
            'request_registration',
        'srvc_type_7afd88b5-d5b5-46d6-b132-8af606f07390_pricing_config_determination_engine':
            'highest',
        srvc_type_pricing_config_for_line_item: '{}',
    },
    all_srvc_prvdrs: [
        {
            label: 'Godrej',
            value: 16,
            full_name: 'Godrej',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'Atmosphere',
            value: 21,
            full_name: 'Temp PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T12',
            value: 18,
            full_name: 'Temp PVT LTD.',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Atmosphere23',
            value: 23,
            full_name: 'Temp13 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Atmosphere1',
            value: 22,
            full_name: 'Temp1 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T2323',
            value: 19,
            full_name: 'T2323',
            icon_path: 'sdasdf',
        },
        {
            label: 'WIFY',
            value: 2,
            full_name: 'Wify Technology',
            icon_path: 'https://home.wify.co.in/assets/logo/wify_logo.png',
        },
        {
            label: 'Test1',
            value: 20,
            full_name: 'Test1 PVT LTD',
            icon_path: '1',
        },
        {
            label: 'test 1',
            value: 34,
            full_name: 'test 1',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T1234',
            value: 35,
            full_name: 'Temp1234 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T122233',
            value: 36,
            full_name: 'Temp122233 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Test1234',
            value: 37,
            full_name: 'Test1234 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'SrvcPrvdr1',
            value: 38,
            full_name: 'SrvcPrvdr1',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr12',
            value: 39,
            full_name: 'SrvcPrvdr12',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr123',
            value: 40,
            full_name: 'SrvcPrvdr123',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr1234',
            value: 41,
            full_name: 'SrvcPrvdr1234',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr12345',
            value: 42,
            full_name: 'SrvcPrvdr12345',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'Godrej2',
            value: 44,
            full_name: 'Godrej2',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'Godre',
            value: 43,
            full_name: 'Godre',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
    ],
    srvcDetails: {
        srvc_id: 16,
        srvc_code: 'leads',
        srvc_desc: 'Potential sales lead',
        srvc_icon: 'icon-revenue-new',
        srvc_title: 'Leads',
    },
    possibleStatus: [
        {
            color: '#e91e63',
            count: 0,
            label: 'Open',
            title: 'Open',
            value: 'open',
        },
        {
            color: '#03a9f4',
            count: 6,
            label: 'Assigned',
            title: 'Assigned',
            value: 'wo29HfLp',
        },
        {
            color: '#009688',
            count: 0,
            label: 'Qualified',
            title: 'Qualified',
            value: 'LtOXOoSa',
        },
        {
            color: '#ffc107',
            count: 1,
            label: 'Proposals',
            title: 'Proposals',
            value: 'e856B5rk',
        },
        {
            color: '#4caf50',
            count: 2,
            label: 'Customer',
            title: 'Customer',
            value: 'rHiId3iY',
        },
        {
            color: '#607d8b',
            count: 0,
            label: 'Closed',
            title: 'Closed',
            value: 'closed',
        },
    ],
    sp_config_data: {
        db_id: 3,
        settings_data: {
            org_id: 2,
            usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
            entry_id: 3,
            ip_address: '::1',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            authority_id: [2, 140, 145],
            sbtsk_fr_145: 4,
            sbtsk_fr_146: 13,
            sbtsk_fr_147: 10,
            srvc_type_id: [57, 16],
            vertical_desc: 'TEst',
            sp_authority_2: 140,
            vertical_title: 'LF',
            rating_type_145: 'another_authority',
            vertical_nature: 'project_based',
            enable_sp_rating: true,
            sp_authority_140: 2,
            sp_authority_145: 2,
            sp_rating_type_2: 'another_authority',
            sp_rating_type_140: 'static_user',
            sp_rating_type_145: 'another_authority',
            sp_rating_type_146: 'static_user',
            sp_static_user_140: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_cust_fields_json:
                '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
            sp_rating_template_2: 3,
            sp_rating_template_140: 3,
            sp_rating_template_145: 7,
            srvc_type_enable_billing: true,
            deployment_possible_roles: [145, 146, 147],
            sp_deployment_who_can_edit: [],
            sp_authority_fr_deployment_145: '',
            sp_authority_fr_deployment_146: 2,
            sp_authority_fr_deployment_147: 2,
            srvc_type_tabular_view_columns: [
                'srvc_prvdr',
                'request_priority',
                'request_description',
                'cust_full_name',
                'creation_date',
                'request_req_date',
                'full_address',
                'sbtsks',
                'attachments',
            ],
            deployment_time_slot_lower_limit: '9:00AM',
            deployment_time_slot_upper_limit: '7:00PM',
            sp_rating_type_fr_deployment_145: 'static_user',
            sp_rating_type_fr_deployment_146: 'static_user',
            sp_rating_type_fr_deployment_147: 'authority',
            sp_static_user_fr_deployment_145: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_static_user_fr_deployment_146: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_static_user_fr_deployment_147: {
                key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                label: 'User 2',
                value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            },
            sp_rating_template_fr_deployment145: 7,
            sp_rating_template_fr_deployment146: 7,
            sp_rating_template_fr_deployment147: 3,
            '2_enable_cross_visibility_of_authorities': true,
            '140_enable_cross_visibility_of_authorities': true,
            '145_enable_cross_visibility_of_authorities': true,
            srvc_type_manday_pricing_config_determination_engine: 'highest',
        },
        settings_type: 'SP_CUSTOM_FIELDS',
    },
    spAuthoritiesConfigData: {
        org_id: 2,
        usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
        entry_id: 3,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_id: [2, 140, 145],
        sbtsk_fr_145: 4,
        sbtsk_fr_146: 13,
        sbtsk_fr_147: 10,
        srvc_type_id: [57, 16],
        vertical_desc: 'TEst',
        sp_authority_2: 140,
        vertical_title: 'LF',
        rating_type_145: 'another_authority',
        vertical_nature: 'project_based',
        enable_sp_rating: true,
        sp_authority_140: 2,
        sp_authority_145: 2,
        sp_rating_type_2: 'another_authority',
        sp_rating_type_140: 'static_user',
        sp_rating_type_145: 'another_authority',
        sp_rating_type_146: 'static_user',
        sp_static_user_140: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_cust_fields_json:
            '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
        sp_rating_template_2: 3,
        sp_rating_template_140: 3,
        sp_rating_template_145: 7,
        srvc_type_enable_billing: true,
        deployment_possible_roles: [145, 146, 147],
        sp_deployment_who_can_edit: [],
        sp_authority_fr_deployment_145: '',
        sp_authority_fr_deployment_146: 2,
        sp_authority_fr_deployment_147: 2,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '9:00AM',
        deployment_time_slot_upper_limit: '7:00PM',
        sp_rating_type_fr_deployment_145: 'static_user',
        sp_rating_type_fr_deployment_146: 'static_user',
        sp_rating_type_fr_deployment_147: 'authority',
        sp_static_user_fr_deployment_145: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_146: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_147: {
            key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            label: 'User 2',
            value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
        },
        sp_rating_template_fr_deployment145: 7,
        sp_rating_template_fr_deployment146: 7,
        sp_rating_template_fr_deployment147: 3,
        '2_enable_cross_visibility_of_authorities': true,
        '140_enable_cross_visibility_of_authorities': true,
        '145_enable_cross_visibility_of_authorities': true,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
    },
    filters: {
        creation_srvc_req_date: [
            '2024-04-30T09:17:18.040Z',
            '2024-09-30T09:17:18.040Z',
        ],
    },
    srvc_id: '16',
    orgSettingsData: {
        country_code: '+91',
        country_name: 'India',
        mobile_digit: 10,
        select_country_code: 'IN',
        selected_country_pincode_length: 6,
    },
};
const initialProps = {
    showEditor: false,
    editMode: true,
    editorItem: {
        id: 516,
        org: {
            label: 'Homelane',
            value: 3,
            icon_path: 'https://super.homelane.com/hlico4.ico',
        },
        addr: '413005, Solapur, MAHARASHTRA',
        time: '2024-07-24T10:13:04.390605',
        title: 'HMLL240724397692',
        c_time: '2024-07-24T10:13:04.390605',
        labels: null,
        status: {
            key: 'rHiId3iY',
            color: '#4caf50',
            title: 'Customer',
            status_type: 'DONE',
            transition_date: '2024-09-16T08:30:27.671529',
        },
        priority: 'Normal',
        req_date: null,
        c_by_name: 'Shambhu Choudhary',
        cust_city: 'Solapur',
        cust_name: 'Prathmesh Chiman',
        is_deleted: false,
        srvc_prvdr: '2',
        description: 'rating 4',
        sbtsks_meta: null,
        cust_pincode: '413005',
        ext_order_id: null,
        req_end_time: null,
        srvc_type_id: 16,
        feedback_data: null,
        req_start_time: null,
        Attachment_count: null,
        brand_location_groups: 'Maharashtra',
        fields_data_for_table: [
            {
                attachments: {},
            },
            {
                cust_full_name: 'Prathmesh Chiman',
            },
            {
                request_priority: 'Normal',
            },
            {
                request_description: 'rating 4',
            },
        ],
        prvdr_location_groups: 'MH',
        geocoding_location_data: {
            location: {
                lat: 17.6748607,
                lng: 75.95213,
            },
            location_type: 'APPROXIMATE',
        },
    },
    isCustomerRequests: false,
    srvcConfigData: {
        qty: '',
        rate: '',
        total: '',
        org_id: 3,
        usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        entry_id: 16,
        ip_address: '::1',
        sbtsk_fr_3: 2,
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_3: 115,
        sbtsk_fr_115: 2,
        sbtsk_fr_117: 2,
        sbtsk_fr_118: 3,
        static_user3: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        authority_115: 3,
        enable_rating: true,
        rating_type_3: 'static_user',
        srvc_statuses:
            '{"ACTIVE":[{"key":"open","color":"#e91e63","title":"Open","status_type":"ACTIVE"},{"key":"wo29HfLp","color":"#03a9f4","title":"Assigned","status_type":"ACTIVE"},{"key":"LtOXOoSa","color":"#009688","title":"Qualified","status_type":"ACTIVE"}],"DONE":[{"key":"e856B5rk","color":"#ffc107","title":"Proposals","status_type":"DONE"},{"key":"rHiId3iY","color":"#4caf50","title":"Customer","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#607d8b","title":"Closed","status_type":"CLOSED"}]}',
        srvc_type_key: 'leads',
        srvc_type_desc: 'Potential sales lead',
        srvc_type_name: 'Leads',
        static_user117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        static_user118: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        rating_type_115: 'another_authority',
        rating_type_117: 'static_user',
        rating_type_118: 'static_user',
        srvc_authorities: [3, 115, 117, 118],
        srvc_type_nature: 'project_based',
        srvc_type_prefix: 'HMLL',
        rating_template_3: 6,
        srvc_can_cust_rate: true,
        rating_template_115: 2,
        rating_template_117: 2,
        rating_template_118: 5,
        srvc_appl_sub_tasks: [3],
        srvc_category_field: '577da6e2-7e6b-4e37-8bc7-0736c8bde7fa',
        srvc_possible_prvdrs: [2, 16],
        srvc_cust_fields_json:
            '{"originalFields":[{"id":"b78c6f47-38d4-49d8-bde9-aeb422357210","element":"TextInput","required":false,"label":{"blocks":[{"key":"811ba","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","element":"Files","label":{"blocks":[{"key":"28mkc","text":"Upload Selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","element":"Files","label":{"blocks":[{"key":"ciq9s","text":"upload file","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"71193b52-8ea9-496f-88c4-7feff9f6324e","element":"WIFY_CAMERA","label":{"blocks":[{"key":"19i3p","text":"Take selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","element":"NumberInput","required":false,"label":{"blocks":[{"key":"bc36h","text":"Number","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"48ff354a-2824-4fff-9345-34285605f38f","element":"Tags","required":false,"label":{"blocks":[{"key":"dmrs3","text":"Drop Down","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"d1f5da3e-7b7e-4404-912e-a2415eb08e88","label":"Label1","value":"Value1"},{"id":"425c1cc7-0a13-49f7-bdf5-503766302d5a","label":"Label2","value":"Value2"}]},{"id":"051bbed3-fa45-4822-b11c-f852afd7d7d0","element":"Dropdown","label":{"blocks":[{"key":"6868p","text":"cat1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"2c94a04c-013a-4984-b687-abd1c7e7b943","value":"Option1"},{"id":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d","value":"Option2"}]},{"id":"501499f2-8d57-430c-a3d6-99e88c0c6905","element":"Tags","required":false,"label":{"blocks":[{"key":"dlc9g","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"04534439-8c55-4e3e-b1d0-bf1e9b973b90","label":"Label1","value":"Value1"},{"id":"c5d05b33-ea66-4012-b654-f79d2339be42","label":"Label2","value":"Value2"}]},{"id":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","element":"Dropdown","label":{"blocks":[{"key":"5if67","text":"cat 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"7e2fb8e4-5939-43da-9250-840f5c76110e","value":"Option1"},{"id":"0fd9fea2-c3f6-40f3-9385-bf085cb53175","value":"Option2"}]},{"id":"383d429b-7bdc-4f1c-83d7-098992bdefb9","element":"WIFY_BARCODE_SCANNER","label":{"blocks":[{"key":"7r0fd","text":"Barcode","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"b78c6f47-38d4-49d8-bde9-aeb422357210","required":false,"label":"Placeholder Label"},{"key":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","required":false,"label":"Upload Selfie","cust_component":"Files","cust_component_value":""},{"key":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","required":false,"label":"upload file","cust_component":"Files","cust_component_value":""},{"key":"71193b52-8ea9-496f-88c4-7feff9f6324e","required":false,"label":"Take selfie","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","required":false,"label":"Number","widget":"number"},{"key":"48ff354a-2824-4fff-9345-34285605f38f","required":false,"label":"Drop Down","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"051bbed3-fa45-4822-b11c-f852afd7d7d0","required":false,"label":"cat1","widget":"select","options":[{"label":"Option1","value":"2c94a04c-013a-4984-b687-abd1c7e7b943"},{"label":"Option2","value":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d"}]},{"key":"501499f2-8d57-430c-a3d6-99e88c0c6905","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","required":false,"label":"cat 2","widget":"select","options":[{"label":"Option1","value":"7e2fb8e4-5939-43da-9250-840f5c76110e"},{"label":"Option2","value":"0fd9fea2-c3f6-40f3-9385-bf085cb53175"}]},{"key":"383d429b-7bdc-4f1c-83d7-098992bdefb9","required":false,"label":"Barcode","cust_component":"WIFY_BARCODE_SCANNER","cust_component_value":""}]}',
        srvc_default_provider: 2,
        srvc_enable_srvc_prvdr: true,
        srvc_type_billing_type: 'hybrid',
        deployment_who_can_edit: [],
        srvc_type_icon_selector: 'icon-revenue-new',
        srvc_type_enable_billing: true,
        authority_fr_deployment_3: 118,
        daily_update_who_can_edit: [115, 3],
        deployment_possible_roles: [118, 117, 3, 115],
        srvc_type_line_item_config:
            '{"7afd88b5-d5b5-46d6-b132-8af606f07390":{"key":"7afd88b5-d5b5-46d6-b132-8af606f07390","label":"Installation"}}',
        authority_fr_deployment_118: 3,
        dont_send_srvc_feedback_sms: true,
        rating_type_fr_deployment_3: 'authority',
        srvc_cust_pincode_mandatory: true,
        srvc_is_cust_fields_dynamic: false,
        daily_update_will_have_issues: 'Yes',
        rating_type_fr_deployment_117: 'static_user',
        rating_type_fr_deployment_118: 'authority',
        static_user_fr_deployment_117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        daily_update_issue_form_fields:
            '{"originalFields":[{"id":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","element":"Files","label":{"blocks":[{"key":"538a9","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","element":"Dropdown","label":{"blocks":[{"key":"51rtb","text":"single","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"f73c8d87-9175-47a6-8682-03d76e10d8fe","value":"Option1"},{"id":"8486c4d8-885c-41da-a703-a77e47bc6bb1","value":"Option2"}]},{"id":"884742c5-da33-408b-aa44-8a524d16a94b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"1v142","text":"Radio button","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f","label":"Label1","value":"Value1","checked":false},{"id":"eea282d8-bd39-46df-8893-90ee46f29500","label":"Label2","value":"Value2","checked":false}]}],"translatedFields":[{"key":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","required":false,"label":"Placeholder Label","cust_component":"Files","cust_component_value":""},{"key":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","required":false,"label":"single","widget":"select","options":[{"label":"Option1","value":"f73c8d87-9175-47a6-8682-03d76e10d8fe"},{"label":"Option2","value":"8486c4d8-885c-41da-a703-a77e47bc6bb1"}]},{"key":"884742c5-da33-408b-aa44-8a524d16a94b","required":false,"label":"Radio button","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f"},{"label":"Label2","value":"eea282d8-bd39-46df-8893-90ee46f29500"}]}]}',
        srvc_feedback_send_by_whatsapp: true,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        '3_auto_assign_based_on_location': false,
        rating_template_fr_deployment_3: 2,
        srvc_req_cust_pincode_mandatory: false,
        deployment_time_slot_lower_limit: '12:00AM',
        deployment_time_slot_upper_limit: '11:45PM',
        srvc_authority_3_specific_fields: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
        ],
        srvc_feedback_trigger_status_key: 'closed',
        '115_auto_assign_based_on_location': false,
        '117_auto_assign_based_on_location': false,
        '118_auto_assign_based_on_location': false,
        rating_template_fr_deployment_117: 5,
        rating_template_fr_deployment_118: 2,
        select_whatsApp_feedback_template:
            'Dear %cust_name%, Your %display_code% request: %display_code% has been registered. Kindly connect our Welspun customer care %feedback_link% if need help.',
        srvc_authority_115_specific_fields: [],
        srvc_auto_movement_fr_LtOXOoSa_key: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        srvc_auto_movement_fr_e856B5rk_key: [
            '48ff354a-2824-4fff-9345-34285605f38f',
            'eff1a08f-151f-4590-bf71-8cbd4ea6e515',
        ],
        srvc_auto_movement_fr_wo29HfLp_key: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
            '04867f4b-1d13-4020-b2c8-d3a53e38bd45',
        ],
        srvc_type_enable_additional_billing: true,
        srvc_type_pricing_config_for_manday: '{}',
        srvc_type_who_can_sync_srvc_req_prc: [3],
        srvc_type_enable_billing_discounting: true,
        daily_update_track_line_item_progress: 'Yes',
        srvc_enable_srvc_prvdr_to_add_requests: true,
        srvc_type_who_can_send_req_for_billing: [3],
        cnsmr_ntfctn_fr_status_open_sms_enabled: true,
        srvc_type_status_open_notify_authorties: [3, 115],
        '3_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_open_sms_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        auto_status_change_fr_on_field_status_key: 'open',
        daily_update_dynamic_line_item_wise_files: 'Yes',
        '115_enable_cross_visibility_of_authorities': true,
        '117_enable_cross_visibility_of_authorities': true,
        '118_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_enabled: false,
        cnsmr_ntfctn_fr_status_open_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_enabled: true,
        srvc_type_who_can_lock_srvc_req_for_billing: [3],
        srvc_type_who_will_get_notified_for_billing: [3],
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_template:
            'Dears %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_template:
            'Dear %cust_name%, Pureit technician %technician_name% is assigned to you for your service request 1234. Contact: %technician_masked_number%, PIN: %masked_number_pin%.',
        srvc_authority_based_movement_status_LtOXOoSa: [],
        srvc_authority_based_movement_status_e856B5rk: [117],
        srvc_authority_based_movement_status_wo29HfLp: [3, 115],
        '3_show_authorities_that_report_to_the_assigner': false,
        hide_authority_selection_from_specific_details: false,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_status_type: 'schedule_assignment',
        srvc_type_categorize_specific_fields_for_billing: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        cnsmr_ntfctn_fr_status_open_whatsapp_message_enabled: false,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_status_type:
            'request_registration',
        'srvc_type_7afd88b5-d5b5-46d6-b132-8af606f07390_pricing_config_determination_engine':
            'highest',
        srvc_type_pricing_config_for_line_item: '{}',
    },
    all_srvc_prvdrs: [
        {
            label: 'Godrej',
            value: 16,
            full_name: 'Godrej',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'Atmosphere',
            value: 21,
            full_name: 'Temp PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T12',
            value: 18,
            full_name: 'Temp PVT LTD.',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Atmosphere23',
            value: 23,
            full_name: 'Temp13 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Atmosphere1',
            value: 22,
            full_name: 'Temp1 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T2323',
            value: 19,
            full_name: 'T2323',
            icon_path: 'sdasdf',
        },
        {
            label: 'WIFY',
            value: 2,
            full_name: 'Wify Technology',
            icon_path: 'https://home.wify.co.in/assets/logo/wify_logo.png',
        },
        {
            label: 'Test1',
            value: 20,
            full_name: 'Test1 PVT LTD',
            icon_path: '1',
        },
        {
            label: 'test 1',
            value: 34,
            full_name: 'test 1',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T1234',
            value: 35,
            full_name: 'Temp1234 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'T122233',
            value: 36,
            full_name: 'Temp122233 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'Test1234',
            value: 37,
            full_name: 'Test1234 PVT LTD',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'SrvcPrvdr1',
            value: 38,
            full_name: 'SrvcPrvdr1',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr12',
            value: 39,
            full_name: 'SrvcPrvdr12',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr123',
            value: 40,
            full_name: 'SrvcPrvdr123',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr1234',
            value: 41,
            full_name: 'SrvcPrvdr1234',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr12345',
            value: 42,
            full_name: 'SrvcPrvdr12345',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'Godrej2',
            value: 44,
            full_name: 'Godrej2',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'Godre',
            value: 43,
            full_name: 'Godre',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
    ],
    srvcDetails: {
        srvc_id: 16,
        srvc_code: 'leads',
        srvc_desc: 'Potential sales lead',
        srvc_icon: 'icon-revenue-new',
        srvc_title: 'Leads',
    },
    possibleStatus: [
        {
            color: '#e91e63',
            count: 0,
            label: 'Open',
            title: 'Open',
            value: 'open',
        },
        {
            color: '#03a9f4',
            count: 6,
            label: 'Assigned',
            title: 'Assigned',
            value: 'wo29HfLp',
        },
        {
            color: '#009688',
            count: 0,
            label: 'Qualified',
            title: 'Qualified',
            value: 'LtOXOoSa',
        },
        {
            color: '#ffc107',
            count: 1,
            label: 'Proposals',
            title: 'Proposals',
            value: 'e856B5rk',
        },
        {
            color: '#4caf50',
            count: 2,
            label: 'Customer',
            title: 'Customer',
            value: 'rHiId3iY',
        },
        {
            color: '#607d8b',
            count: 0,
            label: 'Closed',
            title: 'Closed',
            value: 'closed',
        },
    ],
    sp_config_data: {
        db_id: 3,
        settings_data: {
            org_id: 2,
            usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
            entry_id: 3,
            ip_address: '::1',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            authority_id: [2, 140, 145],
            sbtsk_fr_145: 4,
            sbtsk_fr_146: 13,
            sbtsk_fr_147: 10,
            srvc_type_id: [57, 16],
            vertical_desc: 'TEst',
            sp_authority_2: 140,
            vertical_title: 'LF',
            rating_type_145: 'another_authority',
            vertical_nature: 'project_based',
            enable_sp_rating: true,
            sp_authority_140: 2,
            sp_authority_145: 2,
            sp_rating_type_2: 'another_authority',
            sp_rating_type_140: 'static_user',
            sp_rating_type_145: 'another_authority',
            sp_rating_type_146: 'static_user',
            sp_static_user_140: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_cust_fields_json:
                '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
            sp_rating_template_2: 3,
            sp_rating_template_140: 3,
            sp_rating_template_145: 7,
            srvc_type_enable_billing: true,
            deployment_possible_roles: [145, 146, 147],
            sp_deployment_who_can_edit: [],
            sp_authority_fr_deployment_145: '',
            sp_authority_fr_deployment_146: 2,
            sp_authority_fr_deployment_147: 2,
            srvc_type_tabular_view_columns: [
                'srvc_prvdr',
                'request_priority',
                'request_description',
                'cust_full_name',
                'creation_date',
                'request_req_date',
                'full_address',
                'sbtsks',
                'attachments',
            ],
            deployment_time_slot_lower_limit: '9:00AM',
            deployment_time_slot_upper_limit: '7:00PM',
            sp_rating_type_fr_deployment_145: 'static_user',
            sp_rating_type_fr_deployment_146: 'static_user',
            sp_rating_type_fr_deployment_147: 'authority',
            sp_static_user_fr_deployment_145: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_static_user_fr_deployment_146: {
                key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                label: 'Gaurav Pangam wify(Service provider admin)',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
            },
            sp_static_user_fr_deployment_147: {
                key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                label: 'User 2',
                value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            },
            sp_rating_template_fr_deployment145: 7,
            sp_rating_template_fr_deployment146: 7,
            sp_rating_template_fr_deployment147: 3,
            '2_enable_cross_visibility_of_authorities': true,
            '140_enable_cross_visibility_of_authorities': true,
            '145_enable_cross_visibility_of_authorities': true,
            srvc_type_manday_pricing_config_determination_engine: 'highest',
        },
        settings_type: 'SP_CUSTOM_FIELDS',
    },
    spAuthoritiesConfigData: {
        org_id: 2,
        usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
        entry_id: 3,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_id: [2, 140, 145],
        sbtsk_fr_145: 4,
        sbtsk_fr_146: 13,
        sbtsk_fr_147: 10,
        srvc_type_id: [57, 16],
        vertical_desc: 'TEst',
        sp_authority_2: 140,
        vertical_title: 'LF',
        rating_type_145: 'another_authority',
        vertical_nature: 'project_based',
        enable_sp_rating: true,
        sp_authority_140: 2,
        sp_authority_145: 2,
        sp_rating_type_2: 'another_authority',
        sp_rating_type_140: 'static_user',
        sp_rating_type_145: 'another_authority',
        sp_rating_type_146: 'static_user',
        sp_static_user_140: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_cust_fields_json:
            '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
        sp_rating_template_2: 3,
        sp_rating_template_140: 3,
        sp_rating_template_145: 7,
        srvc_type_enable_billing: true,
        deployment_possible_roles: [145, 146, 147],
        sp_deployment_who_can_edit: [],
        sp_authority_fr_deployment_145: '',
        sp_authority_fr_deployment_146: 2,
        sp_authority_fr_deployment_147: 2,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '9:00AM',
        deployment_time_slot_upper_limit: '7:00PM',
        sp_rating_type_fr_deployment_145: 'static_user',
        sp_rating_type_fr_deployment_146: 'static_user',
        sp_rating_type_fr_deployment_147: 'authority',
        sp_static_user_fr_deployment_145: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_146: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_147: {
            key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            label: 'User 2',
            value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
        },
        sp_rating_template_fr_deployment145: 7,
        sp_rating_template_fr_deployment146: 7,
        sp_rating_template_fr_deployment147: 3,
        '2_enable_cross_visibility_of_authorities': true,
        '140_enable_cross_visibility_of_authorities': true,
        '145_enable_cross_visibility_of_authorities': true,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
    },
    filters: {
        creation_srvc_req_date: [
            '2024-04-30T09:30:23.338Z',
            '2024-09-30T09:30:23.338Z',
        ],
    },
    srvc_id: '16',
    orgSettingsData: {
        country_code: '+91',
        country_name: 'India',
        mobile_digit: 10,
        select_country_code: 'IN',
        selected_country_pincode_length: 6,
    },
};
const initialPropsFrSPLogin = {
    showEditor: false,
    editMode: true,
    isCustomerRequests: true,
    all_srvc_prvdrs: [
        {
            label: 'Godrej',
            value: 16,
            full_name: 'Godrej',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'T12',
            value: 18,
            full_name: 'Temp PVT LTD.',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'SrvcPrvdr12',
            value: 39,
            full_name: 'SrvcPrvdr12',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr123',
            value: 40,
            full_name: 'SrvcPrvdr123',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr1234',
            value: 41,
            full_name: 'SrvcPrvdr1234',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
    ],
    sp_config_data: [],
    filters: {
        assgn_to_prvdr_date: [
            '2024-06-05T06:39:12.559Z',
            '2024-11-05T06:39:12.559Z',
        ],
    },
    srvc_id: '0',
};
const propsFrSPLogin = {
    showEditor: true,
    editMode: true,
    editorItem: {
        id: 520,
        org: {
            label: 'Homelane',
            value: 3,
            icon_path: 'https://super.homelane.com/hlico4.ico',
        },
        addr: '',
        time: '2024-10-16T07:58:12.491414',
        title: 'HMLL241016521022',
        c_time: '2024-10-16T07:58:12.491414',
        labels: null,
        status: {
            key: 'open',
            color: '#e91e63',
            title: 'Open',
            status_type: 'ACTIVE',
            transition_date: '2024-10-16T07:58:12.491414',
        },
        priority: 'Normal',
        req_date: null,
        c_by_name: 'Gaurav Pangam wify',
        cust_city: null,
        cust_name: 'Prathmesh Chiman',
        is_deleted: false,
        srvc_prvdr: '2',
        description: 'Rating',
        sbtsks_meta: [
            {
                sbtsk_id: 1095,
                gai_rating: null,
                sbtsk_status: 'Assigned',
                sbtsk_type_icon: 'icon-map-directions',
                sbtsk_type_title: 'WIFY VISIT',
                sbtsk_status_color: '#fa8c16',
            },
        ],
        cust_pincode: null,
        ext_order_id: null,
        req_end_time: null,
        srvc_type_id: 16,
        feedback_data: null,
        req_start_time: null,
        Attachment_count: null,
        brand_location_groups: null,
        fields_data_for_table: [],
        prvdr_location_groups: null,
        geocoding_location_data: null,
    },
    isCustomerRequests: true,
    srvcConfigData: {
        qty: '',
        rate: '',
        total: '',
        org_id: 3,
        usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        entry_id: 16,
        ip_address: '::1',
        sbtsk_fr_3: 2,
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        authority_3: 115,
        sbtsk_fr_115: 2,
        sbtsk_fr_117: 2,
        sbtsk_fr_118: 3,
        static_user3: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        authority_115: 3,
        enable_rating: true,
        rating_type_3: 'static_user',
        srvc_statuses:
            '{"ACTIVE":[{"key":"open","color":"#e91e63","title":"Open","status_type":"ACTIVE"},{"key":"wo29HfLp","color":"#03a9f4","title":"Assigned","status_type":"ACTIVE"},{"key":"LtOXOoSa","color":"#009688","title":"Qualified","status_type":"ACTIVE"}],"DONE":[{"key":"e856B5rk","color":"#ffc107","title":"Proposals","status_type":"DONE"},{"key":"rHiId3iY","color":"#4caf50","title":"Customer","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#607d8b","title":"Closed","status_type":"CLOSED"}]}',
        srvc_type_key: 'leads',
        srvc_type_desc: 'Potential sales lead',
        srvc_type_name: 'Leads',
        static_user117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        static_user118: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        rating_type_115: 'another_authority',
        rating_type_117: 'static_user',
        rating_type_118: 'static_user',
        srvc_authorities: [3, 115, 117, 118],
        srvc_type_nature: 'project_based',
        srvc_type_prefix: 'HMLL',
        rating_template_3: 6,
        srvc_can_cust_rate: true,
        rating_template_115: 2,
        rating_template_117: 2,
        rating_template_118: 5,
        srvc_appl_sub_tasks: [3],
        srvc_category_field: '577da6e2-7e6b-4e37-8bc7-0736c8bde7fa',
        srvc_possible_prvdrs: [2, 16],
        show_line_items_to_sp: true,
        srvc_cust_fields_json:
            '{"originalFields":[{"id":"b78c6f47-38d4-49d8-bde9-aeb422357210","element":"TextInput","required":false,"label":{"blocks":[{"key":"811ba","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","element":"Files","label":{"blocks":[{"key":"28mkc","text":"Upload Selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","element":"Files","label":{"blocks":[{"key":"ciq9s","text":"upload file","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"71193b52-8ea9-496f-88c4-7feff9f6324e","element":"WIFY_CAMERA","label":{"blocks":[{"key":"19i3p","text":"Take selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","element":"NumberInput","required":false,"label":{"blocks":[{"key":"bc36h","text":"Number","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"48ff354a-2824-4fff-9345-34285605f38f","element":"Tags","required":false,"label":{"blocks":[{"key":"dmrs3","text":"Drop Down","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"d1f5da3e-7b7e-4404-912e-a2415eb08e88","label":"Label1","value":"Value1"},{"id":"425c1cc7-0a13-49f7-bdf5-503766302d5a","label":"Label2","value":"Value2"}]},{"id":"051bbed3-fa45-4822-b11c-f852afd7d7d0","element":"Dropdown","label":{"blocks":[{"key":"6868p","text":"cat1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"2c94a04c-013a-4984-b687-abd1c7e7b943","value":"Option1"},{"id":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d","value":"Option2"}]},{"id":"501499f2-8d57-430c-a3d6-99e88c0c6905","element":"Tags","required":false,"label":{"blocks":[{"key":"dlc9g","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"04534439-8c55-4e3e-b1d0-bf1e9b973b90","label":"Label1","value":"Value1"},{"id":"c5d05b33-ea66-4012-b654-f79d2339be42","label":"Label2","value":"Value2"}]},{"id":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","element":"Dropdown","label":{"blocks":[{"key":"5if67","text":"cat 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"7e2fb8e4-5939-43da-9250-840f5c76110e","value":"Option1"},{"id":"0fd9fea2-c3f6-40f3-9385-bf085cb53175","value":"Option2"}]},{"id":"383d429b-7bdc-4f1c-83d7-098992bdefb9","element":"WIFY_BARCODE_SCANNER","label":{"blocks":[{"key":"7r0fd","text":"Barcode","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"b78c6f47-38d4-49d8-bde9-aeb422357210","required":false,"label":"Placeholder Label"},{"key":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","required":false,"label":"Upload Selfie","cust_component":"Files","cust_component_value":""},{"key":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","required":false,"label":"upload file","cust_component":"Files","cust_component_value":""},{"key":"71193b52-8ea9-496f-88c4-7feff9f6324e","required":false,"label":"Take selfie","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","required":false,"label":"Number","widget":"number"},{"key":"48ff354a-2824-4fff-9345-34285605f38f","required":false,"label":"Drop Down","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"051bbed3-fa45-4822-b11c-f852afd7d7d0","required":false,"label":"cat1","widget":"select","options":[{"label":"Option1","value":"2c94a04c-013a-4984-b687-abd1c7e7b943"},{"label":"Option2","value":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d"}]},{"key":"501499f2-8d57-430c-a3d6-99e88c0c6905","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","required":false,"label":"cat 2","widget":"select","options":[{"label":"Option1","value":"7e2fb8e4-5939-43da-9250-840f5c76110e"},{"label":"Option2","value":"0fd9fea2-c3f6-40f3-9385-bf085cb53175"}]},{"key":"383d429b-7bdc-4f1c-83d7-098992bdefb9","required":false,"label":"Barcode","cust_component":"WIFY_BARCODE_SCANNER","cust_component_value":""}]}',
        srvc_default_provider: 2,
        srvc_enable_srvc_prvdr: true,
        srvc_type_billing_type: 'hybrid',
        deployment_who_can_edit: [],
        srvc_type_icon_selector: 'icon-revenue-new',
        srvc_type_enable_billing: true,
        authority_fr_deployment_3: 118,
        daily_update_who_can_edit: [115, 3],
        deployment_possible_roles: [118, 117, 3, 115],
        srvc_type_line_item_config:
            '{"7afd88b5-d5b5-46d6-b132-8af606f07390":{"key":"7afd88b5-d5b5-46d6-b132-8af606f07390","label":"Installation"}}',
        authority_fr_deployment_118: 3,
        dont_send_srvc_feedback_sms: true,
        rating_type_fr_deployment_3: 'authority',
        srvc_cust_pincode_mandatory: true,
        srvc_is_cust_fields_dynamic: false,
        daily_update_will_have_issues: 'Yes',
        rating_type_fr_deployment_117: 'static_user',
        rating_type_fr_deployment_118: 'authority',
        static_user_fr_deployment_117: {
            key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            label: 'Shambhu Choudhary(software developer)',
            value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
        },
        daily_update_issue_form_fields:
            '{"originalFields":[{"id":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","element":"Files","label":{"blocks":[{"key":"538a9","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","element":"Dropdown","label":{"blocks":[{"key":"51rtb","text":"single","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"f73c8d87-9175-47a6-8682-03d76e10d8fe","value":"Option1"},{"id":"8486c4d8-885c-41da-a703-a77e47bc6bb1","value":"Option2"}]},{"id":"884742c5-da33-408b-aa44-8a524d16a94b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"1v142","text":"Radio button","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f","label":"Label1","value":"Value1","checked":false},{"id":"eea282d8-bd39-46df-8893-90ee46f29500","label":"Label2","value":"Value2","checked":false}]}],"translatedFields":[{"key":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","required":false,"label":"Placeholder Label","cust_component":"Files","cust_component_value":""},{"key":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","required":false,"label":"single","widget":"select","options":[{"label":"Option1","value":"f73c8d87-9175-47a6-8682-03d76e10d8fe"},{"label":"Option2","value":"8486c4d8-885c-41da-a703-a77e47bc6bb1"}]},{"key":"884742c5-da33-408b-aa44-8a524d16a94b","required":false,"label":"Radio button","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f"},{"label":"Label2","value":"eea282d8-bd39-46df-8893-90ee46f29500"}]}]}',
        srvc_feedback_send_by_whatsapp: true,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        '3_auto_assign_based_on_location': false,
        rating_template_fr_deployment_3: 2,
        srvc_req_cust_pincode_mandatory: false,
        deployment_time_slot_lower_limit: '12:00AM',
        deployment_time_slot_upper_limit: '11:45PM',
        srvc_authority_3_specific_fields: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
        ],
        srvc_feedback_trigger_status_key: 'closed',
        '115_auto_assign_based_on_location': false,
        '117_auto_assign_based_on_location': false,
        '118_auto_assign_based_on_location': false,
        rating_template_fr_deployment_117: 5,
        rating_template_fr_deployment_118: 2,
        select_whatsApp_feedback_template:
            'Dear %cust_name%, Your %display_code% request: %display_code% has been registered. Kindly connect our Welspun customer care %feedback_link% if need help.',
        srvc_authority_115_specific_fields: [],
        srvc_auto_movement_fr_LtOXOoSa_key: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        srvc_auto_movement_fr_e856B5rk_key: [
            '48ff354a-2824-4fff-9345-34285605f38f',
            'eff1a08f-151f-4590-bf71-8cbd4ea6e515',
        ],
        srvc_auto_movement_fr_wo29HfLp_key: [
            'b78c6f47-38d4-49d8-bde9-aeb422357210',
            '04867f4b-1d13-4020-b2c8-d3a53e38bd45',
        ],
        srvc_type_enable_additional_billing: true,
        srvc_type_pricing_config_for_manday:
            '{"srvc_type_pricing_config_for_manday":{"input_table_id":"20f910d0-d615-495c-b5a1-fc35b196503d","key":"20f910d0-d615-495c-b5a1-fc35b196503d","manday_master_rate":800}}',
        srvc_type_who_can_sync_srvc_req_prc: [3],
        srvc_type_enable_billing_discounting: true,
        daily_update_track_line_item_progress: 'Yes',
        srvc_enable_srvc_prvdr_to_add_requests: true,
        srvc_type_who_can_send_req_for_billing: [3],
        cnsmr_ntfctn_fr_status_open_sms_enabled: true,
        srvc_type_status_open_notify_authorties: [3, 115],
        '3_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_open_sms_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        auto_status_change_fr_on_field_status_key: 'open',
        daily_update_dynamic_line_item_wise_files: 'Yes',
        '115_enable_cross_visibility_of_authorities': true,
        '117_enable_cross_visibility_of_authorities': true,
        '118_enable_cross_visibility_of_authorities': true,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_enabled: false,
        cnsmr_ntfctn_fr_status_open_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_enabled: true,
        srvc_type_who_can_lock_srvc_req_for_billing: [3],
        srvc_type_who_will_get_notified_for_billing: [3],
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_template:
            'Dears %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_template:
            'Dear %cust_name%, Pureit technician %technician_name% is assigned to you for your service request 1234. Contact: %technician_masked_number%, PIN: %masked_number_pin%.',
        srvc_authority_based_movement_status_LtOXOoSa: [],
        srvc_authority_based_movement_status_e856B5rk: [117],
        srvc_authority_based_movement_status_wo29HfLp: [3, 115],
        '3_show_authorities_that_report_to_the_assigner': false,
        hide_authority_selection_from_specific_details: false,
        cnsmr_ntfctn_fr_status_LtOXOoSa_sms_status_type: 'request_registration',
        cnsmr_ntfctn_fr_status_wo29HfLp_sms_status_type: 'schedule_assignment',
        srvc_type_categorize_specific_fields_for_billing: [
            '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
        ],
        cnsmr_ntfctn_fr_status_open_whatsapp_message_enabled: false,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_template:
            'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
        cnsmr_ntfctn_fr_status_open_whatsapp_message_status_type:
            'request_registration',
        'srvc_type_7afd88b5-d5b5-46d6-b132-8af606f07390_pricing_config_determination_engine':
            'highest',
    },
    all_srvc_prvdrs: [
        {
            label: 'Godrej',
            value: 16,
            full_name: 'Godrej',
            icon_path: 'https://www.godrej.com/img/svg/godrej-logo.svg',
        },
        {
            label: 'T12',
            value: 18,
            full_name: 'Temp PVT LTD.',
            icon_path:
                'https://cdn.shopify.com/s/files/1/0560/9517/8944/files/favicon_32x32.png?v=1620048493',
        },
        {
            label: 'SrvcPrvdr12',
            value: 39,
            full_name: 'SrvcPrvdr12',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr123',
            value: 40,
            full_name: 'SrvcPrvdr123',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
        {
            label: 'SrvcPrvdr1234',
            value: 41,
            full_name: 'SrvcPrvdr1234',
            icon_path:
                'https://tms.wify.co.in/static/media/wify_logo.197f6c21.png',
        },
    ],
    srvcDetails: {
        srvc_id: 16,
        srvc_code: 'leads',
        srvc_desc: 'Potential sales lead',
        srvc_icon: 'icon-revenue-new',
        srvc_title: 'Leads',
        srvc_config: {
            qty: '',
            rate: '',
            total: '',
            org_id: 3,
            usr_id: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            entry_id: 16,
            ip_address: '::1',
            sbtsk_fr_3: 2,
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            authority_3: 115,
            sbtsk_fr_115: 2,
            sbtsk_fr_117: 2,
            sbtsk_fr_118: 3,
            static_user3: {
                key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                label: 'Shambhu Choudhary(software developer)',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            },
            authority_115: 3,
            enable_rating: true,
            rating_type_3: 'static_user',
            srvc_statuses:
                '{"ACTIVE":[{"key":"open","color":"#e91e63","title":"Open","status_type":"ACTIVE"},{"key":"wo29HfLp","color":"#03a9f4","title":"Assigned","status_type":"ACTIVE"},{"key":"LtOXOoSa","color":"#009688","title":"Qualified","status_type":"ACTIVE"}],"DONE":[{"key":"e856B5rk","color":"#ffc107","title":"Proposals","status_type":"DONE"},{"key":"rHiId3iY","color":"#4caf50","title":"Customer","status_type":"DONE"}],"CLOSED":[{"key":"closed","color":"#607d8b","title":"Closed","status_type":"CLOSED"}]}',
            srvc_type_key: 'leads',
            srvc_type_desc: 'Potential sales lead',
            srvc_type_name: 'Leads',
            static_user117: {
                key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                label: 'Shambhu Choudhary(software developer)',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            },
            static_user118: {
                key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                label: 'Shambhu Choudhary(software developer)',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            },
            rating_type_115: 'another_authority',
            rating_type_117: 'static_user',
            rating_type_118: 'static_user',
            srvc_authorities: [3, 115, 117, 118],
            srvc_type_nature: 'project_based',
            srvc_type_prefix: 'HMLL',
            rating_template_3: 6,
            srvc_can_cust_rate: true,
            rating_template_115: 2,
            rating_template_117: 2,
            rating_template_118: 5,
            srvc_appl_sub_tasks: [3],
            srvc_category_field: '577da6e2-7e6b-4e37-8bc7-0736c8bde7fa',
            srvc_possible_prvdrs: [2, 16],
            show_line_items_to_sp: true,
            srvc_cust_fields_json:
                '{"originalFields":[{"id":"b78c6f47-38d4-49d8-bde9-aeb422357210","element":"TextInput","required":false,"label":{"blocks":[{"key":"811ba","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","element":"Files","label":{"blocks":[{"key":"28mkc","text":"Upload Selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","element":"Files","label":{"blocks":[{"key":"ciq9s","text":"upload file","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"71193b52-8ea9-496f-88c4-7feff9f6324e","element":"WIFY_CAMERA","label":{"blocks":[{"key":"19i3p","text":"Take selfie","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","element":"NumberInput","required":false,"label":{"blocks":[{"key":"bc36h","text":"Number","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"48ff354a-2824-4fff-9345-34285605f38f","element":"Tags","required":false,"label":{"blocks":[{"key":"dmrs3","text":"Drop Down","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"d1f5da3e-7b7e-4404-912e-a2415eb08e88","label":"Label1","value":"Value1"},{"id":"425c1cc7-0a13-49f7-bdf5-503766302d5a","label":"Label2","value":"Value2"}]},{"id":"051bbed3-fa45-4822-b11c-f852afd7d7d0","element":"Dropdown","label":{"blocks":[{"key":"6868p","text":"cat1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"2c94a04c-013a-4984-b687-abd1c7e7b943","value":"Option1"},{"id":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d","value":"Option2"}]},{"id":"501499f2-8d57-430c-a3d6-99e88c0c6905","element":"Tags","required":false,"label":{"blocks":[{"key":"dlc9g","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"04534439-8c55-4e3e-b1d0-bf1e9b973b90","label":"Label1","value":"Value1"},{"id":"c5d05b33-ea66-4012-b654-f79d2339be42","label":"Label2","value":"Value2"}]},{"id":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","element":"Dropdown","label":{"blocks":[{"key":"5if67","text":"cat 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"7e2fb8e4-5939-43da-9250-840f5c76110e","value":"Option1"},{"id":"0fd9fea2-c3f6-40f3-9385-bf085cb53175","value":"Option2"}]},{"id":"383d429b-7bdc-4f1c-83d7-098992bdefb9","element":"WIFY_BARCODE_SCANNER","label":{"blocks":[{"key":"7r0fd","text":"Barcode","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false}],"translatedFields":[{"key":"b78c6f47-38d4-49d8-bde9-aeb422357210","required":false,"label":"Placeholder Label"},{"key":"04867f4b-1d13-4020-b2c8-d3a53e38bd45","required":false,"label":"Upload Selfie","cust_component":"Files","cust_component_value":""},{"key":"eff1a08f-151f-4590-bf71-8cbd4ea6e515","required":false,"label":"upload file","cust_component":"Files","cust_component_value":""},{"key":"71193b52-8ea9-496f-88c4-7feff9f6324e","required":false,"label":"Take selfie","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"89060886-aa17-4bcf-8edf-f4d5ba9939b5","required":false,"label":"Number","widget":"number"},{"key":"48ff354a-2824-4fff-9345-34285605f38f","required":false,"label":"Drop Down","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"051bbed3-fa45-4822-b11c-f852afd7d7d0","required":false,"label":"cat1","widget":"select","options":[{"label":"Option1","value":"2c94a04c-013a-4984-b687-abd1c7e7b943"},{"label":"Option2","value":"d73ca39c-fff2-4ce4-8cf3-9ee53210b18d"}]},{"key":"501499f2-8d57-430c-a3d6-99e88c0c6905","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Label1","value":"Value1"},{"label":"Label2","value":"Value2"}],"widgetProps":{"mode":"multiple"}},{"key":"577da6e2-7e6b-4e37-8bc7-0736c8bde7fa","required":false,"label":"cat 2","widget":"select","options":[{"label":"Option1","value":"7e2fb8e4-5939-43da-9250-840f5c76110e"},{"label":"Option2","value":"0fd9fea2-c3f6-40f3-9385-bf085cb53175"}]},{"key":"383d429b-7bdc-4f1c-83d7-098992bdefb9","required":false,"label":"Barcode","cust_component":"WIFY_BARCODE_SCANNER","cust_component_value":""}]}',
            srvc_default_provider: 2,
            srvc_enable_srvc_prvdr: true,
            srvc_type_billing_type: 'hybrid',
            deployment_who_can_edit: [],
            srvc_type_icon_selector: 'icon-revenue-new',
            srvc_type_enable_billing: true,
            authority_fr_deployment_3: 118,
            daily_update_who_can_edit: [115, 3],
            deployment_possible_roles: [118, 117, 3, 115],
            srvc_type_line_item_config:
                '{"7afd88b5-d5b5-46d6-b132-8af606f07390":{"key":"7afd88b5-d5b5-46d6-b132-8af606f07390","label":"Installation"}}',
            authority_fr_deployment_118: 3,
            dont_send_srvc_feedback_sms: true,
            rating_type_fr_deployment_3: 'authority',
            srvc_cust_pincode_mandatory: true,
            srvc_is_cust_fields_dynamic: false,
            daily_update_will_have_issues: 'Yes',
            rating_type_fr_deployment_117: 'static_user',
            rating_type_fr_deployment_118: 'authority',
            static_user_fr_deployment_117: {
                key: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                label: 'Shambhu Choudhary(software developer)',
                value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
            },
            daily_update_issue_form_fields:
                '{"originalFields":[{"id":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","element":"Files","label":{"blocks":[{"key":"538a9","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","element":"Dropdown","label":{"blocks":[{"key":"51rtb","text":"single","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"f73c8d87-9175-47a6-8682-03d76e10d8fe","value":"Option1"},{"id":"8486c4d8-885c-41da-a703-a77e47bc6bb1","value":"Option2"}]},{"id":"884742c5-da33-408b-aa44-8a524d16a94b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"1v142","text":"Radio button","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f","label":"Label1","value":"Value1","checked":false},{"id":"eea282d8-bd39-46df-8893-90ee46f29500","label":"Label2","value":"Value2","checked":false}]}],"translatedFields":[{"key":"2b651b40-d1b1-4a78-8742-560dd0f0ecf1","required":false,"label":"Placeholder Label","cust_component":"Files","cust_component_value":""},{"key":"f9c12a2d-8fd3-46df-83c8-3a32e4123e08","required":false,"label":"single","widget":"select","options":[{"label":"Option1","value":"f73c8d87-9175-47a6-8682-03d76e10d8fe"},{"label":"Option2","value":"8486c4d8-885c-41da-a703-a77e47bc6bb1"}]},{"key":"884742c5-da33-408b-aa44-8a524d16a94b","required":false,"label":"Radio button","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"66cd9de2-c601-48cd-8ad0-0d7547d8cf0f"},{"label":"Label2","value":"eea282d8-bd39-46df-8893-90ee46f29500"}]}]}',
            srvc_feedback_send_by_whatsapp: true,
            srvc_type_tabular_view_columns: [
                'srvc_prvdr',
                'request_priority',
                'request_description',
                'cust_full_name',
                'creation_date',
                'request_req_date',
                'full_address',
                'sbtsks',
                'attachments',
            ],
            '3_auto_assign_based_on_location': false,
            rating_template_fr_deployment_3: 2,
            srvc_req_cust_pincode_mandatory: false,
            deployment_time_slot_lower_limit: '12:00AM',
            deployment_time_slot_upper_limit: '11:45PM',
            srvc_authority_3_specific_fields: [
                'b78c6f47-38d4-49d8-bde9-aeb422357210',
            ],
            srvc_feedback_trigger_status_key: 'closed',
            '115_auto_assign_based_on_location': false,
            '117_auto_assign_based_on_location': false,
            '118_auto_assign_based_on_location': false,
            rating_template_fr_deployment_117: 5,
            rating_template_fr_deployment_118: 2,
            select_whatsApp_feedback_template:
                'Dear %cust_name%, Your %display_code% request: %display_code% has been registered. Kindly connect our Welspun customer care %feedback_link% if need help.',
            srvc_authority_115_specific_fields: [],
            srvc_auto_movement_fr_LtOXOoSa_key: [
                '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
            ],
            srvc_auto_movement_fr_e856B5rk_key: [
                '48ff354a-2824-4fff-9345-34285605f38f',
                'eff1a08f-151f-4590-bf71-8cbd4ea6e515',
            ],
            srvc_auto_movement_fr_wo29HfLp_key: [
                'b78c6f47-38d4-49d8-bde9-aeb422357210',
                '04867f4b-1d13-4020-b2c8-d3a53e38bd45',
            ],
            srvc_type_enable_additional_billing: true,
            srvc_type_pricing_config_for_manday:
                '{"srvc_type_pricing_config_for_manday":{"input_table_id":"20f910d0-d615-495c-b5a1-fc35b196503d","key":"20f910d0-d615-495c-b5a1-fc35b196503d","manday_master_rate":800}}',
            srvc_type_who_can_sync_srvc_req_prc: [3],
            srvc_type_enable_billing_discounting: true,
            daily_update_track_line_item_progress: 'Yes',
            srvc_enable_srvc_prvdr_to_add_requests: true,
            srvc_type_who_can_send_req_for_billing: [3],
            cnsmr_ntfctn_fr_status_open_sms_enabled: true,
            srvc_type_status_open_notify_authorties: [3, 115],
            '3_enable_cross_visibility_of_authorities': true,
            cnsmr_ntfctn_fr_status_open_sms_template:
                'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
            auto_status_change_fr_on_field_status_key: 'open',
            daily_update_dynamic_line_item_wise_files: 'Yes',
            '115_enable_cross_visibility_of_authorities': true,
            '117_enable_cross_visibility_of_authorities': true,
            '118_enable_cross_visibility_of_authorities': true,
            cnsmr_ntfctn_fr_status_LtOXOoSa_sms_enabled: false,
            cnsmr_ntfctn_fr_status_open_sms_status_type: 'request_registration',
            cnsmr_ntfctn_fr_status_wo29HfLp_sms_enabled: true,
            srvc_type_who_can_lock_srvc_req_for_billing: [3],
            srvc_type_who_will_get_notified_for_billing: [3],
            cnsmr_ntfctn_fr_status_LtOXOoSa_sms_template:
                'Dears %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
            cnsmr_ntfctn_fr_status_wo29HfLp_sms_template:
                'Dear %cust_name%, Pureit technician %technician_name% is assigned to you for your service request 1234. Contact: %technician_masked_number%, PIN: %masked_number_pin%.',
            srvc_authority_based_movement_status_LtOXOoSa: [],
            srvc_authority_based_movement_status_e856B5rk: [117],
            srvc_authority_based_movement_status_wo29HfLp: [3, 115],
            '3_show_authorities_that_report_to_the_assigner': false,
            hide_authority_selection_from_specific_details: false,
            cnsmr_ntfctn_fr_status_LtOXOoSa_sms_status_type:
                'request_registration',
            cnsmr_ntfctn_fr_status_wo29HfLp_sms_status_type:
                'schedule_assignment',
            srvc_type_categorize_specific_fields_for_billing: [
                '89060886-aa17-4bcf-8edf-f4d5ba9939b5',
            ],
            cnsmr_ntfctn_fr_status_open_whatsapp_message_enabled: false,
            srvc_type_manday_pricing_config_determination_engine: 'highest',
            cnsmr_ntfctn_fr_status_open_whatsapp_message_template:
                'Dear %cust_name%, Your request no:%display_code% has been registered with WIFY and will be attended shortly.',
            cnsmr_ntfctn_fr_status_open_whatsapp_message_status_type:
                'request_registration',
            'srvc_type_7afd88b5-d5b5-46d6-b132-8af606f07390_pricing_config_determination_engine':
                'highest',
        },
    },
    possibleStatus: [
        {
            key: 'open',
            color: '#e91e63',
            title: 'Open',
            status_type: 'ACTIVE',
            value: 'open',
            label: 'Open',
        },
        {
            key: 'wo29HfLp',
            color: '#03a9f4',
            title: 'Assigned',
            status_type: 'ACTIVE',
            value: 'wo29HfLp',
            label: 'Assigned',
        },
        {
            key: 'LtOXOoSa',
            color: '#009688',
            title: 'Qualified',
            status_type: 'ACTIVE',
            value: 'LtOXOoSa',
            label: 'Qualified',
        },
        {
            key: 'e856B5rk',
            color: '#ffc107',
            title: 'Proposals',
            status_type: 'DONE',
            value: 'e856B5rk',
            label: 'Proposals',
        },
        {
            key: 'rHiId3iY',
            color: '#4caf50',
            title: 'Customer',
            status_type: 'DONE',
            value: 'rHiId3iY',
            label: 'Customer',
        },
        {
            key: 'closed',
            color: '#607d8b',
            title: 'Closed',
            status_type: 'CLOSED',
            value: 'closed',
            label: 'Closed',
        },
    ],
    sp_config_data: [],
    filters: {
        assgn_to_prvdr_date: [
            '2024-06-05T06:39:12.559Z',
            '2024-11-05T06:39:12.559Z',
        ],
    },
    srvc_id: '0',
};
const viewDataFrSPLogin = {
    statuses: [
        {
            color: '#e91e63',
            label: 'Open',
            title: 'Open',
            value: 'open',
        },
        {
            color: '#03a9f4',
            label: 'Assigned',
            title: 'Assigned',
            value: 'wo29HfLp',
        },
        {
            color: '#009688',
            label: 'Qualified',
            title: 'Qualified',
            value: 'LtOXOoSa',
        },
        {
            color: '#ffc107',
            label: 'Proposals',
            title: 'Proposals',
            value: 'e856B5rk',
        },
        {
            color: '#4caf50',
            label: 'Customer',
            title: 'Customer',
            value: 'rHiId3iY',
        },
        {
            color: '#607d8b',
            label: 'Closed',
            title: 'Closed',
            value: 'closed',
        },
    ],
    role_list: [
        {
            label: 'Admin',
            value: 2,
        },
        {
            label: 'Customer service',
            value: 140,
        },
        {
            label: 'L1 Technician',
            value: 145,
        },
        {
            label: 'L2 technician',
            value: 146,
        },
        {
            label: 'L3 Technician',
            value: 147,
        },
    ],
    srvc_type_id: 16,
    sp_config_data: [
        {
            db_id: 3,
            settings_data: {
                qty: '',
                rate: '',
                total: '',
                org_id: 2,
                usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
                entry_id: 3,
                ip_address: '::1',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                authority_id: [2, 140, 145],
                sbtsk_fr_145: 4,
                sbtsk_fr_146: 13,
                sbtsk_fr_147: 10,
                srvc_type_id: [57, 16],
                vertical_desc: 'TEst',
                sp_authority_2: 140,
                vertical_title: 'LF',
                rating_type_145: 'another_authority',
                vertical_nature: 'project_based',
                enable_sp_rating: true,
                sp_authority_140: 2,
                sp_authority_145: 2,
                sp_rating_type_2: 'another_authority',
                sp_rating_type_140: 'static_user',
                sp_rating_type_145: 'another_authority',
                sp_rating_type_146: 'static_user',
                sp_static_user_140: {
                    key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    label: 'Gaurav Pangam wify(Service provider admin)',
                    value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                },
                sp_cust_fields_json:
                    '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
                sp_rating_template_2: 3,
                sp_rating_template_140: 3,
                sp_rating_template_145: 7,
                srvc_type_enable_billing: true,
                deployment_possible_roles: [145, 146, 147],
                sp_deployment_who_can_edit: [],
                srvc_type_line_item_config:
                    '{"a3ee4e46-3882-4306-8660-4c97d1bc4601":{"key":"a3ee4e46-3882-4306-8660-4c97d1bc4601","label":"Installation","fields":"{\\"originalFields\\":[{\\"id\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"element\\":\\"Dropdown\\",\\"label\\":{\\"blocks\\":[{\\"key\\":\\"408n8\\",\\"text\\":\\"Area\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"required\\":false,\\"options\\":[{\\"id\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\",\\"value\\":\\"Chair\\"},{\\"id\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\",\\"value\\":\\"Table\\"},{\\"id\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\",\\"value\\":\\"Sofa\\"}]},{\\"id\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"8fp6r\\",\\"text\\":\\"height\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0},{\\"id\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"6eg50\\",\\"text\\":\\"width\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0}],\\"translatedFields\\":[{\\"key\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"required\\":false,\\"label\\":\\"Area\\",\\"widget\\":\\"select\\",\\"options\\":[{\\"label\\":\\"Chair\\",\\"value\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\"},{\\"label\\":\\"Table\\",\\"value\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\"},{\\"label\\":\\"Sofa\\",\\"value\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\"}]},{\\"key\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"required\\":false,\\"label\\":\\"height\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"required\\":false,\\"label\\":\\"width\\",\\"widget\\":\\"number\\"}]}","name_field_label":"Area","name_field_formula":"{Area} - {height}","quantity_field_label":"quantity","quantity_field_formula":"{width} + {height}","price_field_label":"Price per Sqft"}}',
                sp_authority_fr_deployment_145: '',
                sp_authority_fr_deployment_146: 2,
                sp_authority_fr_deployment_147: 2,
                srvc_type_tabular_view_columns: [
                    'srvc_prvdr',
                    'request_priority',
                    'request_description',
                    'cust_full_name',
                    'creation_date',
                    'request_req_date',
                    'full_address',
                    'sbtsks',
                    'attachments',
                ],
                deployment_time_slot_lower_limit: '9:00AM',
                deployment_time_slot_upper_limit: '7:00PM',
                sp_rating_type_fr_deployment_145: 'static_user',
                sp_rating_type_fr_deployment_146: 'static_user',
                sp_rating_type_fr_deployment_147: 'authority',
                sp_static_user_fr_deployment_145: {
                    key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    label: 'Gaurav Pangam wify(Service provider admin)',
                    value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                },
                sp_static_user_fr_deployment_146: {
                    key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    label: 'Gaurav Pangam wify(Service provider admin)',
                    value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                },
                sp_static_user_fr_deployment_147: {
                    key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                    label: 'User 2',
                    value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                },
                sp_rating_template_fr_deployment145: 7,
                sp_rating_template_fr_deployment146: 7,
                sp_rating_template_fr_deployment147: 3,
                '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': '',
                '923c761f-001b-43b5-ba66-1c07a6a7165e':
                    'b5fdf909-aaab-421c-a4de-b93f727d4657',
                'a9de49bc-95e4-445b-8b28-8db6e748f865': '',
                '2_enable_cross_visibility_of_authorities': true,
                '140_enable_cross_visibility_of_authorities': true,
                '145_enable_cross_visibility_of_authorities': true,
                srvc_type_manday_pricing_config_determination_engine: 'highest',
                srvc_type_pricing_config_for_line_item: '{}',
                srvc_type_pricing_config_for_manday: '{}',
            },
            settings_type: 'SP_CUSTOM_FIELDS',
        },
    ],
    role_list_vs_users: {
        2: [
            {
                label: 'Amul',
                value: '2c2cf620-61aa-468d-9bf8-e4dbc65114df',
                role_id: 2,
            },
            {
                label: 'Gaurav Pangam wify',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                role_id: 2,
            },
            {
                label: 'Test',
                value: '2c2a4ac1-8f1d-417e-bc0f-f1f2c7cf84f0',
                role_id: 2,
            },
            {
                label: 'User 0',
                value: '6781cf1c-41d5-473d-abda-cdbe82d36324',
                role_id: 2,
            },
            {
                label: 'User 1',
                value: '2e549c45-a3aa-491d-8cfb-99d13117f935',
                role_id: 2,
            },
            {
                label: 'User 11',
                value: '1c69862f-cac4-4ed6-95e0-acb0526737e3',
                role_id: 2,
            },
            {
                label: 'User 8',
                value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                role_id: 2,
            },
        ],
        140: [
            {
                label: 'Test',
                value: '2c2a4ac1-8f1d-417e-bc0f-f1f2c7cf84f0',
                role_id: 140,
            },
            {
                label: 'User 0',
                value: '6781cf1c-41d5-473d-abda-cdbe82d36324',
                role_id: 140,
            },
            {
                label: 'User 11',
                value: '1c69862f-cac4-4ed6-95e0-acb0526737e3',
                role_id: 140,
            },
            {
                label: 'User 16',
                value: 'a4822fbe-474e-437b-a78c-34edca74ca4b',
                role_id: 140,
            },
            {
                label: 'user 2',
                value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                role_id: 140,
            },
            {
                label: 'User 4',
                value: 'eb01eab3-7eee-43d8-a050-83f0c1b3f07a',
                role_id: 140,
            },
            {
                label: 'User 8',
                value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                role_id: 140,
            },
        ],
        145: [
            {
                label: 'Amul',
                value: '2c2cf620-61aa-468d-9bf8-e4dbc65114df',
                role_id: 145,
            },
            {
                label: 'User 13',
                value: 'f76dced4-7cc2-4fb9-9002-25e9c15049dc',
                role_id: 145,
            },
            {
                label: 'User 14',
                value: '91c6d8ba-eaa6-4840-8f60-07a875c0e6ca',
                role_id: 145,
            },
            {
                label: 'User 15',
                value: 'd4d798ee-a625-407a-8205-861ccc9026eb',
                role_id: 145,
            },
            {
                label: 'User 16',
                value: 'a4822fbe-474e-437b-a78c-34edca74ca4b',
                role_id: 145,
            },
            {
                label: 'User 8',
                value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                role_id: 145,
            },
            {
                label: 'User 9',
                value: 'd89c6126-f232-46f8-99e3-53ec83f9217e',
                role_id: 145,
            },
        ],
        146: [
            {
                label: 'User 3',
                value: '31e8f0d6-f35c-4cd3-82b2-3fe3519ce29b',
                role_id: 146,
            },
            {
                label: 'User 6',
                value: '97abaa0e-a2dd-4f2c-b927-1b4c6772f28a',
                role_id: 146,
            },
        ],
        147: [
            {
                label: 'New User',
                value: '00f5ffe3-72d8-487f-a0b1-7098e8c88165',
                role_id: 147,
            },
            {
                label: 'User 5',
                value: 'a067d29b-113a-4d9e-abfa-8448777aa437',
                role_id: 147,
            },
            {
                label: 'User 7',
                value: 'f3b6d104-c009-4223-87ab-f90453f42df0',
                role_id: 147,
            },
        ],
    },
    sp_authorities_config_data: {
        qty: '',
        rate: '',
        total: '',
        org_id: 2,
        usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
        entry_id: 3,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        authority_id: [2, 140, 145],
        sbtsk_fr_145: 4,
        sbtsk_fr_146: 13,
        sbtsk_fr_147: 10,
        srvc_type_id: [57, 16],
        vertical_desc: 'TEst',
        sp_authority_2: 140,
        vertical_title: 'LF',
        rating_type_145: 'another_authority',
        vertical_nature: 'project_based',
        enable_sp_rating: true,
        sp_authority_140: 2,
        sp_authority_145: 2,
        sp_rating_type_2: 'another_authority',
        sp_rating_type_140: 'static_user',
        sp_rating_type_145: 'another_authority',
        sp_rating_type_146: 'static_user',
        sp_static_user_140: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_cust_fields_json:
            '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
        sp_rating_template_2: 3,
        sp_rating_template_140: 3,
        sp_rating_template_145: 7,
        srvc_type_enable_billing: true,
        deployment_possible_roles: [145, 146, 147],
        sp_deployment_who_can_edit: [],
        srvc_type_line_item_config:
            '{"a3ee4e46-3882-4306-8660-4c97d1bc4601":{"key":"a3ee4e46-3882-4306-8660-4c97d1bc4601","label":"Installation","fields":"{\\"originalFields\\":[{\\"id\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"element\\":\\"Dropdown\\",\\"label\\":{\\"blocks\\":[{\\"key\\":\\"408n8\\",\\"text\\":\\"Area\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"required\\":false,\\"options\\":[{\\"id\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\",\\"value\\":\\"Chair\\"},{\\"id\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\",\\"value\\":\\"Table\\"},{\\"id\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\",\\"value\\":\\"Sofa\\"}]},{\\"id\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"8fp6r\\",\\"text\\":\\"height\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0},{\\"id\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"6eg50\\",\\"text\\":\\"width\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0}],\\"translatedFields\\":[{\\"key\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"required\\":false,\\"label\\":\\"Area\\",\\"widget\\":\\"select\\",\\"options\\":[{\\"label\\":\\"Chair\\",\\"value\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\"},{\\"label\\":\\"Table\\",\\"value\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\"},{\\"label\\":\\"Sofa\\",\\"value\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\"}]},{\\"key\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"required\\":false,\\"label\\":\\"height\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"required\\":false,\\"label\\":\\"width\\",\\"widget\\":\\"number\\"}]}","name_field_label":"Area","name_field_formula":"{Area} - {height}","quantity_field_label":"quantity","quantity_field_formula":"{width} + {height}","price_field_label":"Price per Sqft"}}',
        sp_authority_fr_deployment_145: '',
        sp_authority_fr_deployment_146: 2,
        sp_authority_fr_deployment_147: 2,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '9:00AM',
        deployment_time_slot_upper_limit: '7:00PM',
        sp_rating_type_fr_deployment_145: 'static_user',
        sp_rating_type_fr_deployment_146: 'static_user',
        sp_rating_type_fr_deployment_147: 'authority',
        sp_static_user_fr_deployment_145: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_146: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_147: {
            key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            label: 'User 2',
            value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
        },
        sp_rating_template_fr_deployment145: 7,
        sp_rating_template_fr_deployment146: 7,
        sp_rating_template_fr_deployment147: 3,
        '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': '',
        '923c761f-001b-43b5-ba66-1c07a6a7165e':
            'b5fdf909-aaab-421c-a4de-b93f727d4657',
        'a9de49bc-95e4-445b-8b28-8db6e748f865': '',
        '2_enable_cross_visibility_of_authorities': true,
        '140_enable_cross_visibility_of_authorities': true,
        '145_enable_cross_visibility_of_authorities': true,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
    },
    role_wise_authorities_users_list: [
        {
            key: 'authority_3',
            label: 'Admin',
            widget: 'select',
            options: [
                {
                    label: 'Abhishek',
                    value: 'b3d9310f-e098-49de-a9ba-e23805994f60',
                    role_id: 3,
                },
                {
                    label: 'Abhishek1',
                    value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                    role_id: 3,
                },
                {
                    label: 'Demo User',
                    value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                    role_id: 3,
                },
                {
                    label: 'Gaurav Pangam',
                    value: '315efff0-a733-40c0-bc1a-fa4b1dc043b3',
                    role_id: 3,
                },
                {
                    label: 'Shambhu Choudhary',
                    value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                    role_id: 3,
                },
                {
                    label: 'Test 1',
                    value: '169986d3-19cc-4dd5-99bd-b2b56bca8b65',
                    role_id: 3,
                },
                {
                    label: 'Test 2',
                    value: '94322d02-1c99-4653-b0ed-c1b0aa5ad45c',
                    role_id: 3,
                },
                {
                    label: 'Test 3',
                    value: '630a4a26-2f41-4cbc-8242-f32d8d5f967d',
                    role_id: 3,
                },
            ],
            role_id: 3,
        },
        {
            key: 'authority_115',
            label: 'Customer service',
            widget: 'select',
            options: [
                {
                    label: 'Abhishek1',
                    value: 'c6d250fa-5176-4c5e-b33a-dd9287d7bb20',
                    role_id: 115,
                },
                {
                    label: 'Balwant Kumar',
                    value: '75490a94-f1de-40f8-a6a9-8ec2481bf493',
                    role_id: 115,
                },
                {
                    label: 'Demo User',
                    value: 'bf376491-f1ce-41e6-a6e1-afd19e8115ba',
                    role_id: 115,
                },
                {
                    label: 'Jainsih',
                    value: '065dd489-701f-4a0a-8656-482988595594',
                    role_id: 115,
                },
                {
                    label: 'Lovnish Bhatia',
                    value: 'dcfd349d-4c33-4c2e-90b2-88d1570ecfc4',
                    role_id: 115,
                },
                {
                    label: 'Shambhu Choudhary',
                    value: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                    role_id: 115,
                },
                {
                    label: 'suraj',
                    value: '99952844-dcac-4392-918f-19b3e13e8532',
                    role_id: 115,
                },
            ],
            role_id: 115,
        },
        {
            key: 'authority_117',
            label: 'Sales',
            widget: 'select',
            options: [
                {
                    label: 'Navin',
                    value: 'ca694780-060f-4ba8-97d5-4d3a352459a3',
                    role_id: 117,
                },
            ],
            role_id: 117,
        },
        {
            key: 'authority_118',
            label: 'Technician',
            widget: 'select',
            options: [
                {
                    label: 'Mandeep Singh',
                    value: '8f9f0a3e-c032-4847-bf73-8effff335250',
                    role_id: 118,
                },
                {
                    label: 'Manoj Shinde',
                    value: '9576d268-7a8c-42f9-8c4a-f6acd957e5da',
                    role_id: 118,
                },
            ],
            role_id: 118,
        },
    ],
    srvc_prvdr_role_wise_authorities_users_list: [
        {
            key: 'authority_2',
            label: 'Admin',
            widget: 'select',
            options: [
                {
                    label: 'Amul',
                    value: '2c2cf620-61aa-468d-9bf8-e4dbc65114df',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'Gaurav Pangam wify',
                    value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'Test',
                    value: '2c2a4ac1-8f1d-417e-bc0f-f1f2c7cf84f0',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 0',
                    value: '6781cf1c-41d5-473d-abda-cdbe82d36324',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 1',
                    value: '2e549c45-a3aa-491d-8cfb-99d13117f935',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 11',
                    value: '1c69862f-cac4-4ed6-95e0-acb0526737e3',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 8',
                    value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
            ],
            role_id: 2,
            is_loc_grp_filteration_enabled_fr_sp: false,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
        {
            key: 'authority_140',
            label: 'Customer service',
            widget: 'select',
            options: [
                {
                    label: 'Test',
                    value: '2c2a4ac1-8f1d-417e-bc0f-f1f2c7cf84f0',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 0',
                    value: '6781cf1c-41d5-473d-abda-cdbe82d36324',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 11',
                    value: '1c69862f-cac4-4ed6-95e0-acb0526737e3',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 16',
                    value: 'a4822fbe-474e-437b-a78c-34edca74ca4b',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'user 2',
                    value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 4',
                    value: 'eb01eab3-7eee-43d8-a050-83f0c1b3f07a',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 8',
                    value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                    role_id: 140,
                    is_loc_grp_matched_with_srvc_req: false,
                },
            ],
            role_id: 140,
            is_loc_grp_filteration_enabled_fr_sp: false,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
        {
            key: 'authority_145',
            label: 'L1 Technician',
            widget: 'select',
            options: [
                {
                    label: 'Amul',
                    value: '2c2cf620-61aa-468d-9bf8-e4dbc65114df',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 13',
                    value: 'f76dced4-7cc2-4fb9-9002-25e9c15049dc',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 14',
                    value: '91c6d8ba-eaa6-4840-8f60-07a875c0e6ca',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 15',
                    value: 'd4d798ee-a625-407a-8205-861ccc9026eb',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 16',
                    value: 'a4822fbe-474e-437b-a78c-34edca74ca4b',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 8',
                    value: 'b7ec7e09-5bd1-4081-8ebb-490358c003cf',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
                {
                    label: 'User 9',
                    value: 'd89c6126-f232-46f8-99e3-53ec83f9217e',
                    role_id: 145,
                    is_loc_grp_matched_with_srvc_req: false,
                },
            ],
            role_id: 145,
            is_loc_grp_filteration_enabled_fr_sp: false,
            widgetProps: {
                showSearch: true,
                optionFilterProp: 'children',
            },
        },
    ],
    form_data: {
        id: 520,
        title: 'HMLL241016521022',
        org_id: 3,
        status: {
            key: 'open',
            color: '#e91e63',
            title: 'Open',
            status_type: 'ACTIVE',
        },
        priority: 'Normal',
        assignees: [['d89c6126-f232-46f8-99e3-53ec83f9217e']],
        form_data: {
            title: 'SMS sent to customer',
            host_d: 'localhost:3000',
            org_id: 2,
            usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
            comment:
                'Dear Prathmesh Chiman, Your request no:HMLL241016521022 has been registered with WIFY and will be attended shortly.',
            entry_id: 520,
            mic_files: {},
            new_prvdr: 2,
            ip_address: '::1',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            attachments: {},
            cust_mobile: '7972083054',
            camera_files: {},
            srvc_type_id: '16',
            cust_full_name: 'Prathmesh Chiman',
            request_priority: 'Normal',
            notification_data: true,
            is_customer_access: 0,
            request_description: 'Rating',
            srvc_type_his_db_id: '337',
            prvdr_assg_timestamp: '2024-10-16T07:58:12.491414',
            sp_srvc_type_his_db_id: '268',
            lastChangedFileSectionIds: [],
        },
        is_deleted: false,
        all_ratings: [],
        sbtsk_types: [
            {
                icon: 'icon-map-directions',
                label: 'WIFY VISIT',
                value: '4',
            },
            {
                icon: 'icon-map-directions',
                label: 'Visit',
                value: '10',
            },
            {
                icon: 'icon-callout',
                label: 'Support',
                value: '11',
            },
            {
                icon: 'icon-add',
                label: 'Flooring measurement',
                value: '12',
            },
            {
                icon: 'icon-anchor',
                label: 'Frontend',
                value: '13',
            },
            {
                icon: 'icon-backtop',
                label: 'Backend',
                value: '14',
            },
            {
                icon: 'icon-all-contacts',
                label: 'Test',
                value: '17',
            },
        ],
        srvc_type_id: 16,
        revisions_data: [],
        sp_all_ratings: [],
        sp_config_data: [
            {
                db_id: 3,
                settings_data: {
                    qty: '',
                    rate: '',
                    total: '',
                    org_id: 2,
                    usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    entry_id: 3,
                    ip_address: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                    authority_id: [2, 140, 145],
                    sbtsk_fr_145: 4,
                    sbtsk_fr_146: 13,
                    sbtsk_fr_147: 10,
                    srvc_type_id: [57, 16],
                    vertical_desc: 'TEst',
                    sp_authority_2: 140,
                    vertical_title: 'LF',
                    rating_type_145: 'another_authority',
                    vertical_nature: 'project_based',
                    enable_sp_rating: true,
                    sp_authority_140: 2,
                    sp_authority_145: 2,
                    sp_rating_type_2: 'another_authority',
                    sp_rating_type_140: 'static_user',
                    sp_rating_type_145: 'another_authority',
                    sp_rating_type_146: 'static_user',
                    sp_static_user_140: {
                        key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                        label: 'Gaurav Pangam wify(Service provider admin)',
                        value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    },
                    sp_cust_fields_json:
                        '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
                    sp_rating_template_2: 3,
                    sp_rating_template_140: 3,
                    sp_rating_template_145: 7,
                    srvc_type_enable_billing: true,
                    deployment_possible_roles: [145, 146, 147],
                    sp_deployment_who_can_edit: [],
                    srvc_type_line_item_config:
                        '{"a3ee4e46-3882-4306-8660-4c97d1bc4601":{"key":"a3ee4e46-3882-4306-8660-4c97d1bc4601","label":"Installation","fields":"{\\"originalFields\\":[{\\"id\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"element\\":\\"Dropdown\\",\\"label\\":{\\"blocks\\":[{\\"key\\":\\"408n8\\",\\"text\\":\\"Area\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"required\\":false,\\"options\\":[{\\"id\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\",\\"value\\":\\"Chair\\"},{\\"id\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\",\\"value\\":\\"Table\\"},{\\"id\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\",\\"value\\":\\"Sofa\\"}]},{\\"id\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"8fp6r\\",\\"text\\":\\"height\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0},{\\"id\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"6eg50\\",\\"text\\":\\"width\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0}],\\"translatedFields\\":[{\\"key\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"required\\":false,\\"label\\":\\"Area\\",\\"widget\\":\\"select\\",\\"options\\":[{\\"label\\":\\"Chair\\",\\"value\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\"},{\\"label\\":\\"Table\\",\\"value\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\"},{\\"label\\":\\"Sofa\\",\\"value\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\"}]},{\\"key\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"required\\":false,\\"label\\":\\"height\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"required\\":false,\\"label\\":\\"width\\",\\"widget\\":\\"number\\"}]}","name_field_label":"Area","name_field_formula":"{Area} - {height}","quantity_field_label":"quantity","quantity_field_formula":"{width} + {height}","price_field_label":"Price per Sqft"}}',
                    sp_authority_fr_deployment_145: '',
                    sp_authority_fr_deployment_146: 2,
                    sp_authority_fr_deployment_147: 2,
                    srvc_type_tabular_view_columns: [
                        'srvc_prvdr',
                        'request_priority',
                        'request_description',
                        'cust_full_name',
                        'creation_date',
                        'request_req_date',
                        'full_address',
                        'sbtsks',
                        'attachments',
                    ],
                    deployment_time_slot_lower_limit: '9:00AM',
                    deployment_time_slot_upper_limit: '7:00PM',
                    sp_rating_type_fr_deployment_145: 'static_user',
                    sp_rating_type_fr_deployment_146: 'static_user',
                    sp_rating_type_fr_deployment_147: 'authority',
                    sp_static_user_fr_deployment_145: {
                        key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                        label: 'Gaurav Pangam wify(Service provider admin)',
                        value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    },
                    sp_static_user_fr_deployment_146: {
                        key: '177886be-ba77-410a-82b2-deb762b8c1c4',
                        label: 'Gaurav Pangam wify(Service provider admin)',
                        value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    },
                    sp_static_user_fr_deployment_147: {
                        key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                        label: 'User 2',
                        value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
                    },
                    sp_rating_template_fr_deployment145: 7,
                    sp_rating_template_fr_deployment146: 7,
                    sp_rating_template_fr_deployment147: 3,
                    '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': '',
                    '923c761f-001b-43b5-ba66-1c07a6a7165e':
                        'b5fdf909-aaab-421c-a4de-b93f727d4657',
                    'a9de49bc-95e4-445b-8b28-8db6e748f865': '',
                    '2_enable_cross_visibility_of_authorities': true,
                    '140_enable_cross_visibility_of_authorities': true,
                    '145_enable_cross_visibility_of_authorities': true,
                    srvc_type_manday_pricing_config_determination_engine:
                        'highest',
                },
                settings_type: 'SP_CUSTOM_FIELDS',
            },
        ],
        srvc_type_title: 'Leads',
        location_grp_ids: [],
        sp_revisions_data: [],
        subtaskConfigData: [
            {
                value: 4,
                statuses: [
                    {
                        color: '#03a9f4',
                        title: 'Started Journey',
                        value: '73hxOHyx',
                    },
                    {
                        color: '#4caf50',
                        title: 'Visit complete',
                        value: 'closed',
                    },
                    {
                        color: '#fa8c16',
                        title: 'Assigned',
                        value: 'open',
                    },
                    {
                        color: '#fa8c16',
                        title: 'Done',
                        value: 'R9WC5Tpt',
                    },
                    {
                        color: '#ffeb3b',
                        title: 'Started work',
                        value: 'A1mo44wc',
                    },
                ],
                config_data: {
                    org_id: 2,
                    usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
                    ip_address: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    sbtsk_statuses:
                        '{"ACTIVE":[{"key":"open","color":"#fa8c16","title":"Assigned","status_type":"ACTIVE"},{"key":"73hxOHyx","color":"#03a9f4","title":"Started Journey","status_type":"ACTIVE"},{"key":"A1mo44wc","color":"#ffeb3b","title":"Started work","status_type":"ACTIVE"}],"DONE":[{"key":"Keu29YYz","title":"Done","color":"#fa8c16"}],"CLOSED":[{"key":"closed","color":"#4caf50","title":"Visit complete","status_type":"CLOSED"}]}',
                    sbtsk_type_key: 'wify_visit',
                    sbtsk_type_desc:
                        'WIFY TEAM visiting customer service requests',
                    sbtsk_type_name: 'WIFY VISIT',
                    mandatory_status: {},
                    sbtsk_is_onfield: true,
                    sbtsk_can_postpone: true,
                    sbtsk_type_icon_selector: 'icon-map-directions',
                    sbtsk_status_closed_fields:
                        '{"originalFields":[{"id":"d3734ff7-e71a-4348-89c2-2ae51dd71956","element":"Dropdown","label":{"blocks":[{"key":"d11im","text":"Issue status ?","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":true,"options":[{"id":"9dd81af4-3b0c-4fac-b11c-8f47d1935fb0","value":"Resolved by visit"},{"id":"b9706d73-ca21-4627-ac59-3aeaec2a42ce","value":"Not covered in company policy"},{"id":"27921ac2-d68e-4325-972e-08dc8b9f3528","value":"Needs replacement"}]},{"id":"f751af60-1775-4adc-8b48-b95307257551","element":"TextArea","required":false,"label":{"blocks":[{"key":"auslh","text":"If not covered in policy, explain why?","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""}],"translatedFields":[{"key":"d3734ff7-e71a-4348-89c2-2ae51dd71956","required":true,"label":"Issue status ?","widget":"select","options":[{"label":"Resolved by visit","value":"9dd81af4-3b0c-4fac-b11c-8f47d1935fb0"},{"label":"Not covered in company policy","value":"b9706d73-ca21-4627-ac59-3aeaec2a42ce"},{"label":"Needs replacement","value":"27921ac2-d68e-4325-972e-08dc8b9f3528"}]},{"key":"f751af60-1775-4adc-8b48-b95307257551","required":false,"label":"If not covered in policy, explain why?","widget":"textarea"}]}',
                    sbtsk_status_for_attendance: '73hxOHyx',
                    enable_role_filter_for_vertical_type_id_2: false,
                    enable_role_filter_for_vertical_type_id_3: false,
                    sbtsk_automation_advance_by_vertical_filter: [3, 2],
                    enable_near_by_filter_fr_vertical_type_of_id_2: false,
                    enable_near_by_filter_fr_vertical_type_of_id_3: false,
                    enable_location_group_matching_for_vertical_type_id_2: false,
                    enable_location_group_matching_for_vertical_type_id_3: false,
                },
            },
        ],
        status_transitions: [
            {
                id: 685,
                key: 'open',
                c_by: 'Gaurav Pangam wify',
                time: '2024-10-16T07:58:12.491414',
                u_by: null,
                c_meta: {
                    time: '2024-10-16T07:58:12.491414',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: null,
                details: {
                    color: '#e91e63',
                    title: 'Open',
                    status: 'open',
                },
            },
        ],
        billing_final_amount: 0,
        download_line_item_data: null,
        srvc_req_locked_by_name: null,
        send_for_billing_by_name: null,
        srvc_req_contact_numbers: [
            {
                key: 'cust_mobile',
                label: 'Primary Number',
                value: '7972083054',
            },
        ],
        discount_approved_by_name: null,
        allow_to_see_line_item_tab: true,
        download_sp_line_item_data: null,
        sp_srvc_req_locked_by_name: null,
        sp_send_for_billing_by_name: null,
        allow_sp_line_items_download: true,
        sp_discount_approved_by_name: null,
        allow_site_attendance_download: true,
        allow_sp_daily_updates_download: true,
        srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday:
                    '{"srvc_type_pricing_config_for_manday":{"input_table_id":"20f910d0-d615-495c-b5a1-fc35b196503d","key":"20f910d0-d615-495c-b5a1-fc35b196503d","manday_master_rate":800}}',
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        sp_srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday: null,
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        is_enable_auto_assign_authorities_refresh_btn: false,
    },
};
const viewDataFrSPLoginFrPayouts = {
    statuses: [
        { color: '#fa8c16', label: 'Open', title: 'Open', value: 'open' },
        {
            color: '#00bcd4',
            label: 'Assigned',
            title: 'Assigned',
            value: 'Eb3zfjqG',
        },
        {
            color: '#f44336',
            label: 'Terminated',
            title: 'Terminated',
            value: 'Kgqupkgy',
        },
        { color: '#E1E1E1', label: 'Closed', title: 'Closed', value: 'closed' },
    ],
    role_list: [
        { label: 'Prinicpal', value: 167 },
        { label: 'Technician', value: 128 },
        { label: 'Ek role bana diya', value: 185 },
        { label: 'Admin', value: 2 },
        { label: 'Helper', value: 182 },
    ],
    srvc_type_id: 37,
    sp_config_data: [
        {
            db_id: 42,
            settings_data: {
                qty: '',
                rate: '',
                total: '',
                org_id: 2,
                usr_id: '6330516a-10ae-4dff-a606-462cb961541f',
                entry_id: 42,
                ip_address: '::1',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                authority_id: [2, 128],
                srvc_type_id: [37],
                vertical_title: 'AYT',
                sp_vendor_roles: [2, 128],
                vertical_nature: 'task_based',
                enable_sp_payouts: true,
                sp_cust_fields_json:
                    '{"originalFields":[{"id":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","element":"Dropdown","label":{"blocks":[{"key":"4de82","text":"single select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"d4ba3577-5a44-4a56-85de-03382cedacd2","value":"1"},{"id":"3fcfe12f-fcf5-4562-8a55-30215622eec0","value":"2"},{"id":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6","value":"3"},{"id":"584674bd-5d57-4221-8585-fc0188f4d919","value":"4"},{"id":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28","value":"5"},{"id":"977cecfd-91ef-41bb-8a11-d69099f977d2","value":"6"}]},{"id":"f81b659c-ea0c-47a1-b505-cd5a578e828b","element":"Tags","required":false,"label":{"blocks":[{"key":"a20le","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"40e76b09-36c0-491b-8139-231cea5738cb","label":"Max","value":"1.5"},{"id":"a89de02d-daff-41c2-8a87-ab8579abbf2d","label":"Joy","value":"13"},{"id":"b76f1eff-efb1-4a2e-92f0-79371041ec0f","label":"Zoe","value":"15"},{"id":"e4038f66-d2a2-48e4-a118-a2204c868ffd","label":"Bruni","value":"11.5"}]},{"id":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","element":"TextInput","required":false,"label":{"blocks":[{"key":"1nntq","text":"test text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"75a0l","text":"Radio","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"944a3809-9802-4d43-bced-57d745bcdb79","label":"Label1","value":"Value1","checked":false},{"id":"0b249d51-2337-4669-a04c-aefc323b97df","label":"Label2","value":"Value2","checked":false}]},{"id":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","element":"TextInput","required":false,"label":{"blocks":[{"key":"4u6j1","text":"I am text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":"","initialText":"","lambdaArn":""},{"id":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","element":"Checkboxes","label":{"blocks":[{"key":"5aoj6","text":"Check Box","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c","value":"Option AA","checked":false},{"id":"50b72ea6-09b8-429f-b6fb-d73b67221324","value":"Option BB","checked":false}]},{"id":"35603e4b-3558-4336-b63c-5379108e50d9","element":"Files","label":{"blocks":[{"key":"6bu9j","text":"File 1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"20c72511-6372-419c-88ca-6c9154060a59","element":"Files","label":{"blocks":[{"key":"895gs","text":"File 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"c85ba677-090e-4721-8e2a-e583dddab9df","element":"Date","label":{"blocks":[{"key":"f2scp","text":"Tarik","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""},{"id":"28274c13-c579-4442-a5b7-415f5b373468","element":"Checkboxes","label":{"blocks":[{"key":"68d8k","text":"Check box empty check","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"b02be6b3-c914-402b-bbc2-90202b7cc872","value":"Check 1","checked":false},{"id":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a","value":"Check 2","checked":false}],"initialText":"","lambdaArn":""}],"translatedFields":[{"key":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","required":false,"label":"single select","widget":"select","options":[{"label":"1","value":"d4ba3577-5a44-4a56-85de-03382cedacd2"},{"label":"2","value":"3fcfe12f-fcf5-4562-8a55-30215622eec0"},{"label":"3","value":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6"},{"label":"4","value":"584674bd-5d57-4221-8585-fc0188f4d919"},{"label":"5","value":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28"},{"label":"6","value":"977cecfd-91ef-41bb-8a11-d69099f977d2"}]},{"key":"f81b659c-ea0c-47a1-b505-cd5a578e828b","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Max","value":"1.5"},{"label":"Joy","value":"13"},{"label":"Zoe","value":"15"},{"label":"Bruni","value":"11.5"}],"widgetProps":{"mode":"multiple"}},{"key":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","required":false,"label":"test text"},{"key":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","required":false,"label":"Radio","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"944a3809-9802-4d43-bced-57d745bcdb79"},{"label":"Label2","value":"0b249d51-2337-4669-a04c-aefc323b97df"}]},{"key":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","required":false,"label":"I am text"},{"key":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","required":false,"label":"Check Box","widget":"checkbox-group","options":[{"label":"Option AA","value":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c"},{"label":"Option BB","value":"50b72ea6-09b8-429f-b6fb-d73b67221324"}]},{"key":"35603e4b-3558-4336-b63c-5379108e50d9","required":false,"label":"File 1","cust_component":"Files","cust_component_value":""},{"key":"20c72511-6372-419c-88ca-6c9154060a59","required":false,"label":"File 2","cust_component":"Files","cust_component_value":""},{"key":"c85ba677-090e-4721-8e2a-e583dddab9df","required":false,"label":"Tarik","widget":"date-picker","widgetProps":{"style":{"width":"100%"}}},{"key":"28274c13-c579-4442-a5b7-415f5b373468","required":false,"label":"Check box empty check","widget":"checkbox-group","options":[{"label":"Check 1","value":"b02be6b3-c914-402b-bbc2-90202b7cc872"},{"label":"Check 2","value":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a"}]}]}',
                srvc_type_enable_billing: true,
                sp_deployment_who_can_edit: [2, 167],
                srvc_type_sp_payouts_config:
                    '{"1236c271-876d-4352-a044-157acbeee076":{"key":"1236c271-876d-4352-a044-157acbeee076","label":"Payouts","fields":"{\\"originalFields\\":[{\\"id\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"fcg9h\\",\\"text\\":\\"QTY\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"},{\\"id\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"ck1r\\",\\"text\\":\\"PRICE PER UNIT\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"}],\\"translatedFields\\":[{\\"key\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"required\\":false,\\"label\\":\\"QTY\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"required\\":false,\\"label\\":\\"PRICE PER UNIT\\",\\"widget\\":\\"number\\"}]}","quantity_field_formula":"{QTY} * {PRICE PER UNIT} + {Labor}","price_field_label":"Labor"}}',
                srvc_type_tabular_view_columns: [
                    'srvc_prvdr',
                    'request_priority',
                    'request_description',
                    'cust_full_name',
                    'creation_date',
                    'request_req_date',
                    'full_address',
                    'sbtsks',
                    'attachments',
                ],
                deployment_time_slot_lower_limit: '12:00AM',
                deployment_time_slot_upper_limit: '11:45PM',
                who_cannot_download_daily_updates: [],
                srvc_type_enable_additional_billing: true,
                'bdc66ac3-3098-44f1-adac-032718faa6c7': '',
                'dead6750-4feb-44b3-824e-6ee4cb4bbc6a': '',
                sp_select_authority_who_can_add_payout: [2, 128],
                '2_enable_cross_visibility_of_authorities': true,
                srvc_type_billing_discounting_rule_config:
                    '[{"key":"fc0d4813-87ad-4147-b1aa-52f4ffdaa858","discount_type":"percentage","approver_type":"static_user","user":{"label":"Jainish Zobalia(Service provider Tech)","value":"089748a6-b631-43f2-89c8-4f6bf3778a98","key":"089748a6-b631-43f2-89c8-4f6bf3778a98"},"per_lower_value":5,"per_higher_value":15}]',
                who_cannot_download_site_level_attendance: [],
                sp_who_will_not_be_able_to_see_the_sp_payout_tab: [182, 167],
            },
            settings_type: 'SP_CUSTOM_FIELDS',
        },
    ],
    role_list_vs_users: {
        2: [
            {
                label: 'Yatharth Verma',
                value: '6330516a-10ae-4dff-a606-462cb961541f',
                role_id: 2,
            },
        ],
        128: [
            {
                label: 'Tufel Mulla',
                value: '82816750-92fb-4994-9245-c2e61f82bab5',
                role_id: 128,
            },
        ],
        167: null,
        182: [
            {
                label: 'Jainish Zobalia is a big boy',
                value: '089748a6-b631-43f2-89c8-4f6bf3778a98',
                role_id: 182,
            },
            {
                label: 'Yatharth Verma',
                value: '6330516a-10ae-4dff-a606-462cb961541f',
                role_id: 182,
            },
        ],
        185: [
            {
                label: 'Gaurav Pangam wify',
                value: '177886be-ba77-410a-82b2-deb762b8c1c4',
                role_id: 185,
            },
        ],
    },
    sp_authorities_config_data: {
        qty: '',
        rate: '',
        total: '',
        org_id: 2,
        usr_id: '6330516a-10ae-4dff-a606-462cb961541f',
        entry_id: 42,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        authority_id: [2, 128],
        srvc_type_id: [37],
        vertical_title: 'AYT',
        sp_vendor_roles: [2, 128],
        vertical_nature: 'task_based',
        enable_sp_payouts: true,
        sp_cust_fields_json:
            '{"originalFields":[{"id":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","element":"Dropdown","label":{"blocks":[{"key":"4de82","text":"single select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"d4ba3577-5a44-4a56-85de-03382cedacd2","value":"1"},{"id":"3fcfe12f-fcf5-4562-8a55-30215622eec0","value":"2"},{"id":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6","value":"3"},{"id":"584674bd-5d57-4221-8585-fc0188f4d919","value":"4"},{"id":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28","value":"5"},{"id":"977cecfd-91ef-41bb-8a11-d69099f977d2","value":"6"}]},{"id":"f81b659c-ea0c-47a1-b505-cd5a578e828b","element":"Tags","required":false,"label":{"blocks":[{"key":"a20le","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"40e76b09-36c0-491b-8139-231cea5738cb","label":"Max","value":"1.5"},{"id":"a89de02d-daff-41c2-8a87-ab8579abbf2d","label":"Joy","value":"13"},{"id":"b76f1eff-efb1-4a2e-92f0-79371041ec0f","label":"Zoe","value":"15"},{"id":"e4038f66-d2a2-48e4-a118-a2204c868ffd","label":"Bruni","value":"11.5"}]},{"id":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","element":"TextInput","required":false,"label":{"blocks":[{"key":"1nntq","text":"test text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"75a0l","text":"Radio","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"944a3809-9802-4d43-bced-57d745bcdb79","label":"Label1","value":"Value1","checked":false},{"id":"0b249d51-2337-4669-a04c-aefc323b97df","label":"Label2","value":"Value2","checked":false}]},{"id":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","element":"TextInput","required":false,"label":{"blocks":[{"key":"4u6j1","text":"I am text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":"","initialText":"","lambdaArn":""},{"id":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","element":"Checkboxes","label":{"blocks":[{"key":"5aoj6","text":"Check Box","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c","value":"Option AA","checked":false},{"id":"50b72ea6-09b8-429f-b6fb-d73b67221324","value":"Option BB","checked":false}]},{"id":"35603e4b-3558-4336-b63c-5379108e50d9","element":"Files","label":{"blocks":[{"key":"6bu9j","text":"File 1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"20c72511-6372-419c-88ca-6c9154060a59","element":"Files","label":{"blocks":[{"key":"895gs","text":"File 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"c85ba677-090e-4721-8e2a-e583dddab9df","element":"Date","label":{"blocks":[{"key":"f2scp","text":"Tarik","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""},{"id":"28274c13-c579-4442-a5b7-415f5b373468","element":"Checkboxes","label":{"blocks":[{"key":"68d8k","text":"Check box empty check","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"b02be6b3-c914-402b-bbc2-90202b7cc872","value":"Check 1","checked":false},{"id":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a","value":"Check 2","checked":false}],"initialText":"","lambdaArn":""}],"translatedFields":[{"key":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","required":false,"label":"single select","widget":"select","options":[{"label":"1","value":"d4ba3577-5a44-4a56-85de-03382cedacd2"},{"label":"2","value":"3fcfe12f-fcf5-4562-8a55-30215622eec0"},{"label":"3","value":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6"},{"label":"4","value":"584674bd-5d57-4221-8585-fc0188f4d919"},{"label":"5","value":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28"},{"label":"6","value":"977cecfd-91ef-41bb-8a11-d69099f977d2"}]},{"key":"f81b659c-ea0c-47a1-b505-cd5a578e828b","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Max","value":"1.5"},{"label":"Joy","value":"13"},{"label":"Zoe","value":"15"},{"label":"Bruni","value":"11.5"}],"widgetProps":{"mode":"multiple"}},{"key":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","required":false,"label":"test text"},{"key":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","required":false,"label":"Radio","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"944a3809-9802-4d43-bced-57d745bcdb79"},{"label":"Label2","value":"0b249d51-2337-4669-a04c-aefc323b97df"}]},{"key":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","required":false,"label":"I am text"},{"key":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","required":false,"label":"Check Box","widget":"checkbox-group","options":[{"label":"Option AA","value":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c"},{"label":"Option BB","value":"50b72ea6-09b8-429f-b6fb-d73b67221324"}]},{"key":"35603e4b-3558-4336-b63c-5379108e50d9","required":false,"label":"File 1","cust_component":"Files","cust_component_value":""},{"key":"20c72511-6372-419c-88ca-6c9154060a59","required":false,"label":"File 2","cust_component":"Files","cust_component_value":""},{"key":"c85ba677-090e-4721-8e2a-e583dddab9df","required":false,"label":"Tarik","widget":"date-picker","widgetProps":{"style":{"width":"100%"}}},{"key":"28274c13-c579-4442-a5b7-415f5b373468","required":false,"label":"Check box empty check","widget":"checkbox-group","options":[{"label":"Check 1","value":"b02be6b3-c914-402b-bbc2-90202b7cc872"},{"label":"Check 2","value":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a"}]}]}',
        srvc_type_enable_billing: true,
        sp_deployment_who_can_edit: [2, 167],
        srvc_type_sp_payouts_config:
            '{"1236c271-876d-4352-a044-157acbeee076":{"key":"1236c271-876d-4352-a044-157acbeee076","label":"Payouts","fields":"{\\"originalFields\\":[{\\"id\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"fcg9h\\",\\"text\\":\\"QTY\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"},{\\"id\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"ck1r\\",\\"text\\":\\"PRICE PER UNIT\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"}],\\"translatedFields\\":[{\\"key\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"required\\":false,\\"label\\":\\"QTY\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"required\\":false,\\"label\\":\\"PRICE PER UNIT\\",\\"widget\\":\\"number\\"}]}","quantity_field_formula":"{QTY} * {PRICE PER UNIT} + {Labor}","price_field_label":"Labor"}}',
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '12:00AM',
        deployment_time_slot_upper_limit: '11:45PM',
        who_cannot_download_daily_updates: [],
        srvc_type_enable_additional_billing: true,
        'bdc66ac3-3098-44f1-adac-032718faa6c7': '',
        'dead6750-4feb-44b3-824e-6ee4cb4bbc6a': '',
        sp_select_authority_who_can_add_payout: [2, 128],
        '2_enable_cross_visibility_of_authorities': true,
        srvc_type_billing_discounting_rule_config:
            '[{"key":"fc0d4813-87ad-4147-b1aa-52f4ffdaa858","discount_type":"percentage","approver_type":"static_user","user":{"label":"Jainish Zobalia(Service provider Tech)","value":"089748a6-b631-43f2-89c8-4f6bf3778a98","key":"089748a6-b631-43f2-89c8-4f6bf3778a98"},"per_lower_value":5,"per_higher_value":15}]',
        who_cannot_download_site_level_attendance: [],
        sp_who_will_not_be_able_to_see_the_sp_payout_tab: [182, 167],
    },
    role_wise_authorities_users_list: [
        {
            key: 'authority_126',
            label: 'Admin',
            widget: 'select',
            options: [
                {
                    label: 'Jainish Zobalia',
                    value: '79e27a57-dfb4-43e2-882f-3880cff1c74a',
                    role_id: 126,
                },
                {
                    label: 'Yatharth Verma',
                    value: '556dffb7-7ff5-420a-aa47-94d1d8f2d064',
                    role_id: 126,
                },
            ],
            role_id: 126,
        },
        {
            key: 'authority_127',
            label: 'L1 Technician',
            widget: 'select',
            options: [
                {
                    label: 'Blame game',
                    value: '8c7404ed-5f0c-4173-9a1c-65ede1c1a2ba',
                    role_id: 127,
                },
                {
                    label: 'Demo technician',
                    value: '1ebb80cd-9abc-426d-9786-9e6d6c3fa65b',
                    role_id: 127,
                },
                {
                    label: 'shambhu',
                    value: '3e612554-493c-4a8a-84ec-e555d78c0081',
                    role_id: 127,
                },
                {
                    label: 'Sudama',
                    value: '0c53ca63-1e73-4b82-85ff-a40507b3f9d1',
                    role_id: 127,
                },
            ],
            role_id: 127,
        },
    ],
    srvc_prvdr_role_wise_authorities_users_list: [
        {
            key: 'authority_2',
            label: 'Admin',
            widget: 'select',
            options: [
                {
                    label: 'Yatharth Verma',
                    value: '6330516a-10ae-4dff-a606-462cb961541f',
                    role_id: 2,
                    is_loc_grp_matched_with_srvc_req: false,
                },
            ],
            role_id: 2,
            is_loc_grp_filteration_enabled_fr_sp: false,
        },
        {
            key: 'authority_128',
            label: 'Technician',
            widget: 'select',
            options: [
                {
                    label: 'Tufel Mulla',
                    value: '82816750-92fb-4994-9245-c2e61f82bab5',
                    role_id: 128,
                    is_loc_grp_matched_with_srvc_req: false,
                },
            ],
            role_id: 128,
            is_loc_grp_filteration_enabled_fr_sp: false,
        },
    ],
    form_data: {
        id: 5935,
        title: 'HUL 241017215520',
        org_id: 11,
        status: {
            key: 'open',
            color: '#fa8c16',
            title: 'Open',
            status_type: 'ACTIVE',
        },
        priority: 'Urgent',
        assignees: [['177886be-ba77-410a-82b2-deb762b8c1c4']],
        form_data: {
            host_d: 'localhost:3000',
            org_id: 2,
            usr_id: '6330516a-10ae-4dff-a606-462cb961541f',
            entry_id: 5935,
            cust_city: null,
            mic_files: {},
            new_prvdr: 2,
            cust_email: null,
            cust_state: null,
            ip_address: '::1',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            attachments: {},
            authority_2: '6330516a-10ae-4dff-a606-462cb961541f',
            cust_line_0: null,
            cust_line_1: null,
            cust_line_2: null,
            cust_line_3: null,
            cust_mobile: '7351783487',
            is_api_call: 1,
            camera_files: {},
            cust_pincode: null,
            ext_order_id: 'API/104',
            srvc_type_id: '37',
            cust_full_name: 'Yatharth',
            sp_final_amount: '1000.00',
            sp_payouts_data: {
                total: 450,
                form_data: {
                    '1236c271-876d-4352-a044-157acbeee076': [
                        {
                            key: 'c95b200a-5145-4f12-89b2-af2adfdb7599',
                            qty: '30.00',
                            rate: 10,
                            total: 300,
                            vendor: '6330516a-10ae-4dff-a606-462cb961541f',
                            input_table_id:
                                'c95b200a-5145-4f12-89b2-af2adfdb7599',
                            'bdc66ac3-3098-44f1-adac-032718faa6c7': 10,
                            'dead6750-4feb-44b3-824e-6ee4cb4bbc6a': 2,
                        },
                        {
                            key: 'd2befad7-1e1e-4802-b95a-251313e75970',
                            qty: '30.00',
                            rate: 5,
                            total: 150,
                            vendor: '6330516a-10ae-4dff-a606-462cb961541f',
                            input_table_id:
                                'd2befad7-1e1e-4802-b95a-251313e75970',
                            'bdc66ac3-3098-44f1-adac-032718faa6c7': 5,
                            'dead6750-4feb-44b3-824e-6ee4cb4bbc6a': 5,
                        },
                    ],
                },
                total_qty: 60,
                '1236c271-876d-4352-a044-157acbeee076_total_qty': '030.0030.00',
            },
            request_priority: 'Urgent',
            is_customer_access: 0,
            sp_final_sub_total: '1000.00',
            update_for_comment: false,
            request_description: 'FR2',
            service_provider_id: 2,
            srvc_type_his_db_id: '317',
            prvdr_assg_timestamp: '2024-10-17T07:19:15.459679',
            sp_srvc_type_his_db_id: '181',
            lastChangedFileSectionIds: [],
            sp_additional_billing_items: {
                total: 1000,
                form_data: {
                    additional_line_item: [
                        {
                            key: '493cecb4-4113-4a81-81ee-f5cc5e30d5dd',
                            qty: 100,
                            rate: 10,
                            total: 1000,
                            input_table_id:
                                '493cecb4-4113-4a81-81ee-f5cc5e30d5dd',
                        },
                    ],
                },
                total_qty: 100,
                additional_line_item_total_qty: 100,
            },
            '79a88c7b-c64f-46c4-a277-bc80efa1c154': 'API/104',
        },
        is_deleted: false,
        all_ratings: [],
        sbtsk_types: [
            { icon: 'icon-map-directions', label: 'WIFY Visit', value: '4' },
            { icon: 'icon-alert', label: 'Check', value: '15' },
        ],
        srvc_type_id: 37,
        revisions_data: [],
        sp_all_ratings: [],
        sp_config_data: [
            {
                db_id: 42,
                settings_data: {
                    qty: '',
                    rate: '',
                    total: '',
                    org_id: 2,
                    usr_id: '6330516a-10ae-4dff-a606-462cb961541f',
                    entry_id: 42,
                    ip_address: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    authority_id: [2, 128],
                    srvc_type_id: [37],
                    vertical_title: 'AYT',
                    sp_vendor_roles: [2, 128],
                    vertical_nature: 'task_based',
                    enable_sp_payouts: true,
                    sp_cust_fields_json:
                        '{"originalFields":[{"id":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","element":"Dropdown","label":{"blocks":[{"key":"4de82","text":"single select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"d4ba3577-5a44-4a56-85de-03382cedacd2","value":"1"},{"id":"3fcfe12f-fcf5-4562-8a55-30215622eec0","value":"2"},{"id":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6","value":"3"},{"id":"584674bd-5d57-4221-8585-fc0188f4d919","value":"4"},{"id":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28","value":"5"},{"id":"977cecfd-91ef-41bb-8a11-d69099f977d2","value":"6"}]},{"id":"f81b659c-ea0c-47a1-b505-cd5a578e828b","element":"Tags","required":false,"label":{"blocks":[{"key":"a20le","text":"Multi Select","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"40e76b09-36c0-491b-8139-231cea5738cb","label":"Max","value":"1.5"},{"id":"a89de02d-daff-41c2-8a87-ab8579abbf2d","label":"Joy","value":"13"},{"id":"b76f1eff-efb1-4a2e-92f0-79371041ec0f","label":"Zoe","value":"15"},{"id":"e4038f66-d2a2-48e4-a118-a2204c868ffd","label":"Bruni","value":"11.5"}]},{"id":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","element":"TextInput","required":false,"label":{"blocks":[{"key":"1nntq","text":"test text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":""},{"id":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","element":"RadioButtons","required":false,"label":{"blocks":[{"key":"75a0l","text":"Radio","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"options":[{"id":"944a3809-9802-4d43-bced-57d745bcdb79","label":"Label1","value":"Value1","checked":false},{"id":"0b249d51-2337-4669-a04c-aefc323b97df","label":"Label2","value":"Value2","checked":false}]},{"id":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","element":"TextInput","required":false,"label":{"blocks":[{"key":"4u6j1","text":"I am text","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":"","initialText":"","lambdaArn":""},{"id":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","element":"Checkboxes","label":{"blocks":[{"key":"5aoj6","text":"Check Box","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c","value":"Option AA","checked":false},{"id":"50b72ea6-09b8-429f-b6fb-d73b67221324","value":"Option BB","checked":false}]},{"id":"35603e4b-3558-4336-b63c-5379108e50d9","element":"Files","label":{"blocks":[{"key":"6bu9j","text":"File 1","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"20c72511-6372-419c-88ca-6c9154060a59","element":"Files","label":{"blocks":[{"key":"895gs","text":"File 2","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"c85ba677-090e-4721-8e2a-e583dddab9df","element":"Date","label":{"blocks":[{"key":"f2scp","text":"Tarik","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""},{"id":"28274c13-c579-4442-a5b7-415f5b373468","element":"Checkboxes","label":{"blocks":[{"key":"68d8k","text":"Check box empty check","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"b02be6b3-c914-402b-bbc2-90202b7cc872","value":"Check 1","checked":false},{"id":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a","value":"Check 2","checked":false}],"initialText":"","lambdaArn":""}],"translatedFields":[{"key":"8ce4ad0d-d572-47ba-9c7e-1bc849b620de","required":false,"label":"single select","widget":"select","options":[{"label":"1","value":"d4ba3577-5a44-4a56-85de-03382cedacd2"},{"label":"2","value":"3fcfe12f-fcf5-4562-8a55-30215622eec0"},{"label":"3","value":"45cd7286-fe50-49b2-9f5b-a4d0fe6913f6"},{"label":"4","value":"584674bd-5d57-4221-8585-fc0188f4d919"},{"label":"5","value":"c7fb8662-1ee3-40d2-93ec-ab2ffe3bcd28"},{"label":"6","value":"977cecfd-91ef-41bb-8a11-d69099f977d2"}]},{"key":"f81b659c-ea0c-47a1-b505-cd5a578e828b","required":false,"label":"Multi Select","widget":"select","options":[{"label":"Max","value":"1.5"},{"label":"Joy","value":"13"},{"label":"Zoe","value":"15"},{"label":"Bruni","value":"11.5"}],"widgetProps":{"mode":"multiple"}},{"key":"2fdd92a6-68ce-408b-be20-96401ae9b4f5","required":false,"label":"test text"},{"key":"a1cc2ff2-b95b-4f1a-9e61-51b78cbb893b","required":false,"label":"Radio","widget":"radio-group","forwardRef":true,"options":[{"label":"Label1","value":"944a3809-9802-4d43-bced-57d745bcdb79"},{"label":"Label2","value":"0b249d51-2337-4669-a04c-aefc323b97df"}]},{"key":"29d2a7aa-21d3-4e71-b240-daff0aea2ce5","required":false,"label":"I am text"},{"key":"9a5b522f-f2d9-480f-8cdd-ab4b3def2ce3","required":false,"label":"Check Box","widget":"checkbox-group","options":[{"label":"Option AA","value":"18c7c8de-6e75-42f1-b7c9-e81d6888c24c"},{"label":"Option BB","value":"50b72ea6-09b8-429f-b6fb-d73b67221324"}]},{"key":"35603e4b-3558-4336-b63c-5379108e50d9","required":false,"label":"File 1","cust_component":"Files","cust_component_value":""},{"key":"20c72511-6372-419c-88ca-6c9154060a59","required":false,"label":"File 2","cust_component":"Files","cust_component_value":""},{"key":"c85ba677-090e-4721-8e2a-e583dddab9df","required":false,"label":"Tarik","widget":"date-picker","widgetProps":{"style":{"width":"100%"}}},{"key":"28274c13-c579-4442-a5b7-415f5b373468","required":false,"label":"Check box empty check","widget":"checkbox-group","options":[{"label":"Check 1","value":"b02be6b3-c914-402b-bbc2-90202b7cc872"},{"label":"Check 2","value":"24bea07c-ebce-4d9c-b8a4-f0d0cb5c1b8a"}]}]}',
                    srvc_type_enable_billing: true,
                    sp_deployment_who_can_edit: [2, 167],
                    srvc_type_sp_payouts_config:
                        '{"1236c271-876d-4352-a044-157acbeee076":{"key":"1236c271-876d-4352-a044-157acbeee076","label":"Payouts","fields":"{\\"originalFields\\":[{\\"id\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"fcg9h\\",\\"text\\":\\"QTY\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"},{\\"id\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"ck1r\\",\\"text\\":\\"PRICE PER UNIT\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0,\\"initialText\\":\\"\\",\\"lambdaArn\\":\\"\\"}],\\"translatedFields\\":[{\\"key\\":\\"dead6750-4feb-44b3-824e-6ee4cb4bbc6a\\",\\"required\\":false,\\"label\\":\\"QTY\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"bdc66ac3-3098-44f1-adac-032718faa6c7\\",\\"required\\":false,\\"label\\":\\"PRICE PER UNIT\\",\\"widget\\":\\"number\\"}]}","quantity_field_formula":"{QTY} * {PRICE PER UNIT} + {Labor}","price_field_label":"Labor"}}',
                    srvc_type_tabular_view_columns: [
                        'srvc_prvdr',
                        'request_priority',
                        'request_description',
                        'cust_full_name',
                        'creation_date',
                        'request_req_date',
                        'full_address',
                        'sbtsks',
                        'attachments',
                    ],
                    deployment_time_slot_lower_limit: '12:00AM',
                    deployment_time_slot_upper_limit: '11:45PM',
                    who_cannot_download_daily_updates: [],
                    srvc_type_enable_additional_billing: true,
                    'bdc66ac3-3098-44f1-adac-032718faa6c7': '',
                    'dead6750-4feb-44b3-824e-6ee4cb4bbc6a': '',
                    sp_select_authority_who_can_add_payout: [2, 128],
                    '2_enable_cross_visibility_of_authorities': true,
                    srvc_type_billing_discounting_rule_config:
                        '[{"key":"fc0d4813-87ad-4147-b1aa-52f4ffdaa858","discount_type":"percentage","approver_type":"static_user","user":{"label":"Jainish Zobalia(Service provider Tech)","value":"089748a6-b631-43f2-89c8-4f6bf3778a98","key":"089748a6-b631-43f2-89c8-4f6bf3778a98"},"per_lower_value":5,"per_higher_value":15}]',
                    who_cannot_download_site_level_attendance: [],
                    sp_who_will_not_be_able_to_see_the_sp_payout_tab: [
                        182, 167,
                    ],
                },
                settings_type: 'SP_CUSTOM_FIELDS',
            },
        ],
        srvc_type_title: 'HUL RFS',
        location_grp_ids: [],
        sp_revisions_data: [],
        subtaskConfigData: [
            {
                value: 4,
                statuses: [
                    { color: '#4caf50', title: 'WIP', value: '7tzKpHp8' },
                    { color: '#E1E1E1', title: 'Closed', value: 'closed' },
                    { color: '#fa8c16', title: 'Open', value: 'open' },
                ],
                config_data: {
                    org_id: 2,
                    usr_id: '6330516a-10ae-4dff-a606-462cb961541f',
                    ip_address: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    sbtsk_statuses:
                        '{"ACTIVE":[{"key":"open","color":"#fa8c16","title":"Open","status_type":"ACTIVE"},{"key":"7tzKpHp8","color":"#4caf50","title":"WIP","status_type":"ACTIVE"}],"CLOSED":[{"key":"closed","color":"#E1E1E1","title":"Closed","status_type":"CLOSED"}]}',
                    sbtsk_type_key: 'wify_visit',
                    sbtsk_type_desc: 'visit task',
                    sbtsk_type_name: 'WIFY Visit',
                    mandatory_status: {},
                    sbtsk_is_onfield: true,
                    sbtsk_status_open_fields:
                        '{"originalFields":[{"id":"7e68c41d-580f-40a3-b2cf-df4a144d021f","element":"WIFY_CAMERA","label":{"blocks":[{"key":"8qo3g","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"a24a9fb6-36aa-4645-90bf-00d1de57fd42","element":"Dropdown","label":{"blocks":[{"key":"2ldio","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"1bc81f23-0c41-458b-a077-a6d10b421236","value":"Option1"},{"id":"d434d70e-7936-499c-9fb6-99794831981e","value":"Option2"}]}],"translatedFields":[{"key":"7e68c41d-580f-40a3-b2cf-df4a144d021f","required":false,"label":"Placeholder Label","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"a24a9fb6-36aa-4645-90bf-00d1de57fd42","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"1bc81f23-0c41-458b-a077-a6d10b421236"},{"label":"Option2","value":"d434d70e-7936-499c-9fb6-99794831981e"}]}]}',
                    sbtsk_type_icon_selector: 'icon-map-directions',
                    sbtsk_status_closed_fields:
                        '{"originalFields":[{"id":"7e68c41d-580f-40a3-b2cf-df4a144d021f","element":"WIFY_CAMERA","label":{"blocks":[{"key":"8qo3g","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false},{"id":"a24a9fb6-36aa-4645-90bf-00d1de57fd42","element":"Dropdown","label":{"blocks":[{"key":"2ldio","text":"sbtsk close","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"1bc81f23-0c41-458b-a077-a6d10b421236","value":"Option1"},{"id":"d434d70e-7936-499c-9fb6-99794831981e","value":"Option2"}],"initialText":"","lambdaArn":""},{"id":"fbd779de-b707-4f27-8b6c-89165baa1698","element":"Date","label":{"blocks":[{"key":"90d8r","text":"sbtskdate","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"initialText":"","lambdaArn":""}],"translatedFields":[{"key":"7e68c41d-580f-40a3-b2cf-df4a144d021f","required":false,"label":"Placeholder Label","cust_component":"WIFY_CAMERA","cust_component_value":""},{"key":"a24a9fb6-36aa-4645-90bf-00d1de57fd42","required":false,"label":"sbtsk close","widget":"select","options":[{"label":"Option1","value":"1bc81f23-0c41-458b-a077-a6d10b421236"},{"label":"Option2","value":"d434d70e-7936-499c-9fb6-99794831981e"}]},{"key":"fbd779de-b707-4f27-8b6c-89165baa1698","required":false,"label":"sbtskdate","widget":"date-picker","widgetProps":{"style":{"width":"100%"}}}]}',
                    sbtsk_status_closed_notify_authorties: [2],
                    hide_subtask_on_service_request_closure: true,
                    make_attachments_field_mandatory_fr_closed: true,
                    sbtsk_automation_advance_by_vertical_filter: [41],
                    disable_default_assignee_filteration_by_reporting_to: true,
                    sbtsk_automation_advance_filteration_by_srvc_type_id: [35],
                    sbtsk_automation_enable_service_type_filteration_for_35: false,
                },
            },
        ],
        status_transitions: [
            {
                id: 6193,
                key: 'open',
                c_by: 'Yatharth Verma',
                time: '2024-10-17T07:17:58.208421',
                u_by: null,
                c_meta: {
                    time: '2024-10-17T07:17:58.208421',
                    ip_addr: '::1',
                    user_agent:
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                u_meta: null,
                details: { color: '#fa8c16', title: 'Open', status: 'open' },
            },
        ],
        billing_final_amount: 1000,
        download_line_item_data: null,
        srvc_req_locked_by_name: null,
        send_for_billing_by_name: null,
        srvc_req_contact_numbers: [
            {
                key: 'cust_mobile',
                label: 'Primary Number',
                value: '7351783487',
            },
        ],
        discount_approved_by_name: null,
        allow_to_add_sp_payout_tab: true,
        allow_to_see_line_item_tab: true,
        allow_to_see_sp_payout_tab: true,
        download_sp_line_item_data: null,
        sp_payout_users_fr_vendors: [
            {
                label: 'Yatharth Verma (WMUN1999)',
                value: '6330516a-10ae-4dff-a606-462cb961541f',
            },
        ],
        sp_srvc_req_locked_by_name: null,
        sp_send_for_billing_by_name: null,
        allow_sp_line_items_download: true,
        sp_discount_approved_by_name: null,
        allow_site_attendance_download: true,
        allow_sp_daily_updates_download: true,
        srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday: null,
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        sp_srvc_type_history_pricing_config: [
            {
                srvc_type_pricing_config_for_manday: null,
                srvc_type_pricing_config_for_line_item: null,
            },
        ],
        is_enable_auto_assign_authorities_refresh_btn: false,
    },
};

describe('Smoke Test for ItemEditor', () => {
    it('renders without crashing', () => {
        render(<ItemEditor />);
    });

    //write a test case for item editor component is SP Ratings not visible
    it('should not display SP Ratings For Brand Login', () => {
        //mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(false);
        // Mock API call to return viewData
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewData });
            }
        );

        const { rerender } = render(<ItemEditor {...initialProps} />);
        //rerender itemEditor
        rerender(<ItemEditor {...props} />);
        // screen.debug(undefined, Infinity);
        // Assert that "SP Ratings" is not in the document
        const SPRatings = screen.queryByText('SP Ratings');
        expect(SPRatings).not.toBeInTheDocument();
    });

    //write a test case for brand lineItem to be visible to SP
    it('should display Brand Line Items For SP Login', async () => {
        //mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        // Mock API call to return viewDataFrSPLogin
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrSPLogin });
            }
        );

        const { rerender } = render(<ItemEditor {...initialPropsFrSPLogin} />);
        //rerender itemEditor
        rerender(<ItemEditor {...propsFrSPLogin} />);

        // Check that "Line Items" is visible in the document
        const lineItems = await screen.findByText(/line items/i);
        expect(lineItems).toBeInTheDocument();
        await act(async () => {
            userEvent.click(lineItems);
        });

        //get all elements by text Total Quantity
        const totalQuantity = screen.getAllByText(/Total Quantity/i);
        expect(totalQuantity).toHaveLength(2);

        //get all elements by text **Unsaved changes(Click save to apply)
        const unsavedMessage = screen.getAllByText(
            '**Unsaved changes(Click save to apply)'
        );
        expect(unsavedMessage).toHaveLength(1);
        // screen.debug(undefined, Infinity);
    });

    //write a test case for brand lineItem to be visible to SP
    it('should display SP payouts in task based vertical', async () => {
        //mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        // Mock API call to return viewDataFrSPLogin
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrSPLoginFrPayouts });
            }
        );

        const { rerender } = render(<ItemEditor {...initialPropsFrSPLogin} />);
        //rerender itemEditor
        rerender(<ItemEditor {...propsFrSPLogin} />);

        // Check that "SP Payouts" is visible in the document
        const spPayouts = await screen.findByText(/sp payouts/i);
        expect(spPayouts).toBeInTheDocument();
        await act(async () => {
            userEvent.click(spPayouts);
        });

        // Locate "Total Net Amount"
        const totalNetAmt = screen.getByText(/Total Net Amount:/i); // Match the text exactly
        expect(totalNetAmt).toBeInTheDocument();
    });
});

describe('ItemEditor Column Configuration', () => {
    it('should set columns to 2 for mobile view or zoom level 125%', () => {
        jest.mock('../../util/helpers', () => ({
            isMobileView: jest.fn().mockReturnValue(true), // Mock to return true for mobile view
            isScreenZoomPercentage125: jest.fn().mockReturnValue(false), // Mock to return false for screen zoom percentage 125
        }));

        const { rerender } = render(<ItemEditor {...initialProps} />);

        // Simulate columns calculation
        const columns =
            isMobileView() || isScreenZoomPercentage125()
                ? 2
                : initialProps.srvcConfigData?.srvc_cust_fields_json_colspan ||
                  2;

        expect(columns).toBe(2);

        jest.mock('../../util/helpers', () => ({
            isMobileView: jest.fn().mockReturnValue(false), // Mock to return false for mobile view
            isScreenZoomPercentage125: jest.fn().mockReturnValue(true), // Mock to return true for screen zoom percentage 125
        }));
        rerender(<ItemEditor {...initialProps} />);

        // Check if columns is still set to 2
        const updatedColumns =
            isMobileView() || isScreenZoomPercentage125()
                ? 2
                : initialProps.srvcConfigData?.srvc_cust_fields_json_colspan ||
                  2;

        expect(updatedColumns).toBe(2);
    });

    it('should use custom column span if not in mobile view or zoom level 125%', () => {
        jest.mock('../../util/helpers', () => ({
            isMobileView: jest.fn().mockReturnValue(false), // Mock to return false for mobile view
            isScreenZoomPercentage125: jest.fn().mockReturnValue(false), // Mock to return false for screen zoom percentage 125
        }));

        const customColumnProps = {
            ...initialProps,
            srvcConfigData: { srvc_cust_fields_json_colspan: 4 },
        };

        // Provide mock props with custom column span
        render(<ItemEditor {...customColumnProps} />);

        // Calculate columns based on the mock data
        const columns =
            isMobileView() || isScreenZoomPercentage125()
                ? 2
                : customColumnProps.srvcConfigData
                      ?.srvc_cust_fields_json_colspan || 2;

        // Assert that columns is set to the custom value
        expect(columns).toBe(4);
    });
});

// to check P&L not visible in the brand-side
// to chek p&L tab not visible to SP side when  p&l config is disabled
// to chek p&L tab not visible to SP side when  p&l config is enabled and no roles selected
// to check P&L tab visible to Sp side when p&l config is enabled and some of roles selected

describe('P&L tab visibility-', () => {
    const prefixProfitLoss = 'profitLoss';
    test(`${prefixProfitLoss} tab not visible when config is disabled in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: SPviewDataFrDisabledPL });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const p_and_l_tab = await screen.queryByText(/P&L/i);
        expect(p_and_l_tab).not.toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab visible when config is enabled and minimum 1role is selected in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(true);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrEnabledPLWithRolesSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const p_and_l_tab = await screen.queryByText(/P&L/i);
        expect(p_and_l_tab).toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab not visible when config is enabled but no role is selected in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrEnabledPLWithNoRolesSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const p_and_l_tab = await screen.queryByText(/P&L/i);
        expect(p_and_l_tab).not.toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab not visible on the brand-side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(false);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrBrand });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const p_and_l_tab = await screen.queryByText(/P&L/i);
        expect(p_and_l_tab).not.toBeInTheDocument();
    });
    test(`${prefixProfitLoss} P&L compeont called after clicking on tab`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(true);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrEnabledPLWithRolesSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const p_and_l_tab = await screen.queryByText(/P&L/i);
        expect(p_and_l_tab).toBeInTheDocument();

        await act(async () => {
            userEvent.click(p_and_l_tab);
        });
        const revenueTable = await screen.queryByText(/ProfitAndLoss/i);
        expect(revenueTable).toBeInTheDocument();
    });
});

describe('P&L tab visibility - Project Based', () => {
    const prefixProfitLoss = 'profitLoss';

    test(`${prefixProfitLoss} tab not visible when config is disabled in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrDisabledPLProjectSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const plTab = await screen.queryByText(/P&L/i);
        expect(plTab).not.toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab visible when config is enabled and minimum 1role is selected in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(true);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataForProjectEnbaledPLWithAuthRoleSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const plTab = await screen.queryByText(/P&L/i);
        expect(plTab).toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab not visible when config is enabled but no role is selected in SP side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataForProjectEnbaledPLWithAuthRoleSP });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const plTab = await screen.queryByText(/P&L/i);
        expect(plTab).not.toBeInTheDocument();
    });

    test(`${prefixProfitLoss} tab not visible on the brand-side`, async () => {
        ConfigHelpers.isServiceProvider.mockReturnValue(false);
        ConfigHelpers.doesUserHaveOneOfTheRole.mockReturnValue(false);

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete) => {
                onComplete({ data: viewDataFrBrand });
            }
        );

        const { rerender } = render(<ItemEditor {...initPropsFrPandLConfig} />);
        rerender(<ItemEditor {...rerenderPropsFrPandLConfig} />);

        const plTab = await screen.queryByText(/P&L/i);
        expect(plTab).not.toBeInTheDocument();
    });
});

jest.mock('../../components/WIFY/WifyComponents/StarRatingCompact', () =>
    jest.fn(({ rated, maxRating }) => (
        <div data-testid="star-rating-compact">
            {`Rating: ${rated} out of ${maxRating}`}
        </div>
    ))
);
describe('ItemEditor - StarRatingCompact conditional rendering', () => {
    it('should render StarRatingCompact component when feedbackData is present', async () => {
        // Mock the API call
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                onComplete({ data: fakeViewData1 });
            }
        );

        const { rerender } = render(
            <ItemEditor {...fakeProps} showEditor={false} />
        );
        // ...
        rerender(<ItemEditor {...fakeProps} showEditor={true} />);

        // Wait for data load and the StarRatingCompact to appear
        await waitFor(() => {
            expect(
                screen.getByTestId('star-rating-compact')
            ).toBeInTheDocument();
        });
    });

    it('should render StarRatingCompact component with 0 rating when feedbackData is present but no ratings given by user', async () => {
        // Mock the API call
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                onComplete({ data: fakeViewData2 });
            }
        );

        const { rerender } = render(
            <ItemEditor {...fakeProps} showEditor={false} />
        );
        // ...
        rerender(<ItemEditor {...fakeProps} showEditor={true} />);

        // Wait for data load and the StarRatingCompact to appear
        await waitFor(() => {
            expect(
                screen.getByTestId('star-rating-compact')
            ).toBeInTheDocument();
        });
        // Rating: 0 out of 10
        await waitFor(() => {
            expect(screen.getByTestId('star-rating-compact')).toHaveTextContent(
                'Rating: 0 out of 10'
            );
        });
    });

    it('should not render StarRatingCompact component when feedbackData is not given by user', async () => {
        // Mock the API call
        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                onComplete({ data: fakeViewData3 });
            }
        );

        const { rerender } = render(
            <ItemEditor {...fakeProps} showEditor={false} />
        );

        rerender(<ItemEditor {...fakeProps} showEditor={true} />);
        await waitFor(() => {
            expect(
                screen.queryByTestId('star-rating-compact')
            ).not.toBeInTheDocument();
        });
    });
});

describe('ItemEditor Capacity API', () => {
    it('should call capacity API when opening modal for new request', async () => {
        // Mock the API calls
        let capacityApiCalled = false;
        let capacityApiParams = null;

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                if (url === '/v1/brands/capacity/new-req') {
                    capacityApiCalled = true;
                    capacityApiParams = params;
                    onComplete({ data: mockCapacityData });
                } else {
                    // Mock other API calls
                    onComplete({ data: viewData });
                }
            }
        );

        const props = {
            showEditor: true,
            editMode: false, // New request
            srvcDetails: { srvc_id: 16, srvc_title: 'Test Service' },
            sp_config_data: mockSpConfigData,
            srvcConfigData: {},
        };

        render(<ItemEditor {...props} />);

        // Wait for capacity API to be called
        await waitFor(() => {
            expect(capacityApiCalled).toBe(true);
        });

        // Verify API was called with correct parameters
        expect(capacityApiParams).toEqual({
            srvc_type_id: 16,
            pincode: 110001,
            product_details: JSON.stringify({
                vertical_id: 123,
            }),
        });
    });

    it('should not call capacity API when vertical ID is missing', async () => {
        let capacityApiCalled = false;

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                if (url === '/v1/brands/capacity/new-req') {
                    capacityApiCalled = true;
                }
                onComplete({ data: viewData });
            }
        );

        const props = {
            showEditor: true,
            editMode: false,
            srvcDetails: { srvc_id: 16, srvc_title: 'Test Service' },
            sp_config_data: {}, // Missing settings_data.db.id
            srvcConfigData: {},
        };

        render(<ItemEditor {...props} />);

        // Wait a bit and verify capacity API was not called
        await new Promise((resolve) => setTimeout(resolve, 100));
        expect(capacityApiCalled).toBe(false);
    });

    it('should not call capacity API when in edit mode', async () => {
        let capacityApiCalled = false;

        http_utils.performGetCall.mockImplementation(
            (url, params, onComplete, onError) => {
                if (url === '/v1/brands/capacity/new-req') {
                    capacityApiCalled = true;
                }
                onComplete({ data: viewData });
            }
        );

        const props = {
            showEditor: true,
            editMode: true, // Edit mode
            srvcDetails: { srvc_id: 16, srvc_title: 'Test Service' },
            sp_config_data: mockSpConfigData,
            srvcConfigData: {},
            editorItem: { id: 123 },
        };

        render(<ItemEditor {...props} />);

        // Wait a bit and verify capacity API was not called
        await new Promise((resolve) => setTimeout(resolve, 100));
        expect(capacityApiCalled).toBe(false);
    });

    it('should handle capacity data from PreSrvcPrvdrSelector', async () => {
        const props = {
            showEditor: true,
            editMode: false,
            srvcDetails: { srvc_id: 16, srvc_title: 'Test Service' },
            sp_config_data: mockSpConfigData,
            srvcConfigData: {},
        };

        const { rerender } = render(<ItemEditor {...props} />);

        // Get the component instance to call handleCapacityDataFetched
        const component = global.itemEditorInstance;

        // Mock capacity data and provider ID
        const mockCapacityDataResponse = [
            {
                category_value: 'electronics',
                slots: [{ start: '2025-04-01T09:00:00Z', available_qty: 3 }],
            },
        ];
        const selectedProviderId = 456;

        // Call the handler method
        if (component && component.handleCapacityDataFetched) {
            component.handleCapacityDataFetched(
                mockCapacityDataResponse,
                selectedProviderId
            );
        }

        // Wait for state update
        await waitFor(() => {
            expect(component.state.capacityData).toEqual(
                mockCapacityDataResponse
            );
            expect(component.state.selectedServiceProviderId).toBe(
                selectedProviderId
            );
        });
    });

    it('should include selected service provider ID in form data when creating request', async () => {
        let submittedData = null;

        http_utils.performPostCall.mockImplementation(
            (url, data, onComplete, onError) => {
                submittedData = data;
                onComplete({ entry_id: 123 });
            }
        );

        const props = {
            showEditor: true,
            editMode: false,
            srvcDetails: { srvc_id: 16, srvc_title: 'Test Service' },
            sp_config_data: mockSpConfigData,
            srvcConfigData: {},
        };

        render(<ItemEditor {...props} />);

        // Get the component instance and set selected provider
        const component = global.itemEditorInstance;
        if (component) {
            component.setState({
                selectedServiceProviderId: 789,
                capacityData: mockCapacityData,
            });

            // Simulate form submission
            const formData = {
                request_description: 'Test request',
                request_priority: 'high',
            };

            component.submitForm(formData);
        }

        // Wait for API call
        await waitFor(() => {
            expect(submittedData).toBeTruthy();
            expect(submittedData.new_prvdr).toBe(789);
            expect(submittedData.capacity_data).toEqual(mockCapacityData);
        });
    });
});
